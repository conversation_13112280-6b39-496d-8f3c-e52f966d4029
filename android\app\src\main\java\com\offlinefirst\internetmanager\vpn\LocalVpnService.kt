package com.offlinefirst.internetmanager.vpn

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.net.VpnService
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import com.offlinefirst.internetmanager.OfflineFirstApplication
import com.offlinefirst.internetmanager.R
import com.offlinefirst.internetmanager.ui.MainActivity
import kotlinx.coroutines.*
import timber.log.Timber
import java.io.FileInputStream
import java.io.FileOutputStream
import java.net.InetSocketAddress
import java.nio.ByteBuffer
import java.nio.channels.DatagramChannel
import java.nio.channels.Selector
import java.nio.channels.SelectionKey
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean

/**
 * خدمة VPN المحلي لاعتراض وإعادة توجيه حركة الإنترنت
 */
class LocalVpnService : VpnService() {
    
    private var vpnInterface: ParcelFileDescriptor? = null
    private var vpnThread: Thread? = null
    private val isRunning = AtomicBoolean(false)
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // خريطة لتتبع الاتصالات النشطة
    private val activeConnections = ConcurrentHashMap<String, VpnConnection>()
    
    // معالج الحزم
    private lateinit var packetProcessor: PacketProcessor
    
    companion object {
        const val ACTION_START_VPN = "com.offlinefirst.internetmanager.START_VPN"
        const val ACTION_STOP_VPN = "com.offlinefirst.internetmanager.STOP_VPN"
        
        // إعدادات VPN
        private const val VPN_ADDRESS = "********"
        private const val VPN_ROUTE = "0.0.0.0"
        private const val VPN_DNS = "*******"
        private const val VPN_MTU = 1500
    }
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("LocalVpnService created")
        
        // إنشاء معالج الحزم
        packetProcessor = PacketProcessor(this)
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("LocalVpnService onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_VPN -> startVpn()
            ACTION_STOP_VPN -> stopVpn()
            else -> startVpn() // افتراضي
        }
        
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Timber.d("LocalVpnService destroyed")
        stopVpn()
        serviceScope.cancel()
    }
    
    /**
     * بدء خدمة VPN
     */
    private fun startVpn() {
        if (isRunning.get()) {
            Timber.d("VPN already running")
            return
        }
        
        try {
            // إنشاء واجهة VPN
            val builder = Builder()
                .setSession(getString(R.string.app_name))
                .addAddress(VPN_ADDRESS, 32)
                .addRoute(VPN_ROUTE, 0)
                .addDnsServer(VPN_DNS)
                .setMtu(VPN_MTU)
                .setBlocking(false)
            
            // إنشاء الواجهة
            vpnInterface = builder.establish()
            
            if (vpnInterface == null) {
                Timber.e("Failed to establish VPN interface")
                return
            }
            
            isRunning.set(true)
            
            // بدء الإشعار المقدم
            startForeground(
                OfflineFirstApplication.NOTIFICATION_VPN_SERVICE,
                createVpnNotification()
            )
            
            // بدء معالجة الحزم
            startPacketProcessing()
            
            Timber.i("VPN service started successfully")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start VPN service")
            stopVpn()
        }
    }
    
    /**
     * إيقاف خدمة VPN
     */
    private fun stopVpn() {
        if (!isRunning.get()) {
            Timber.d("VPN not running")
            return
        }
        
        isRunning.set(false)
        
        try {
            // إيقاف معالجة الحزم
            vpnThread?.interrupt()
            vpnThread = null
            
            // إغلاق الواجهة
            vpnInterface?.close()
            vpnInterface = null
            
            // مسح الاتصالات النشطة
            activeConnections.clear()
            
            // إيقاف الإشعار المقدم
            stopForeground(STOP_FOREGROUND_REMOVE)
            
            Timber.i("VPN service stopped")
            
        } catch (e: Exception) {
            Timber.e(e, "Error stopping VPN service")
        }
    }
    
    /**
     * بدء معالجة الحزم
     */
    private fun startPacketProcessing() {
        vpnThread = Thread {
            try {
                val vpnInput = FileInputStream(vpnInterface!!.fileDescriptor)
                val vpnOutput = FileOutputStream(vpnInterface!!.fileDescriptor)
                
                val buffer = ByteBuffer.allocate(VPN_MTU)
                
                while (isRunning.get() && !Thread.currentThread().isInterrupted) {
                    try {
                        // قراءة حزمة من واجهة VPN
                        buffer.clear()
                        val length = vpnInput.read(buffer.array())
                        
                        if (length > 0) {
                            buffer.limit(length)
                            
                            // معالجة الحزمة
                            serviceScope.launch {
                                processPacket(buffer.duplicate(), vpnOutput)
                            }
                        }
                        
                    } catch (e: Exception) {
                        if (isRunning.get()) {
                            Timber.e(e, "Error processing packet")
                        }
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Error in packet processing thread")
            }
        }
        
        vpnThread?.start()
    }
    
    /**
     * معالجة حزمة واحدة
     */
    private suspend fun processPacket(packet: ByteBuffer, vpnOutput: FileOutputStream) {
        try {
            // تحليل الحزمة
            val parsedPacket = packetProcessor.parsePacket(packet)
            
            if (parsedPacket != null) {
                // معالجة الحزمة حسب النوع
                when (parsedPacket.protocol) {
                    PacketProtocol.TCP -> processTcpPacket(parsedPacket, vpnOutput)
                    PacketProtocol.UDP -> processUdpPacket(parsedPacket, vpnOutput)
                    else -> {
                        // إعادة توجيه الحزمة كما هي
                        forwardPacket(packet, vpnOutput)
                    }
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error processing individual packet")
        }
    }
    
    /**
     * معالجة حزمة TCP
     */
    private suspend fun processTcpPacket(packet: ParsedPacket, vpnOutput: FileOutputStream) {
        // التحقق من أن الحزمة HTTP/HTTPS
        if (packet.destinationPort == 80 || packet.destinationPort == 443) {
            // إعادة توجيه عبر البروكسي المحلي
            packetProcessor.forwardToProxy(packet, vpnOutput)
        } else {
            // إعادة توجيه مباشرة
            forwardPacket(packet.rawData, vpnOutput)
        }
    }
    
    /**
     * معالجة حزمة UDP
     */
    private suspend fun processUdpPacket(packet: ParsedPacket, vpnOutput: FileOutputStream) {
        // معالجة DNS أو إعادة توجيه مباشرة
        if (packet.destinationPort == 53) {
            // معالجة DNS
            packetProcessor.processDnsQuery(packet, vpnOutput)
        } else {
            // إعادة توجيه مباشرة
            forwardPacket(packet.rawData, vpnOutput)
        }
    }
    
    /**
     * إعادة توجيه حزمة مباشرة
     */
    private suspend fun forwardPacket(packet: ByteBuffer, vpnOutput: FileOutputStream) {
        try {
            withContext(Dispatchers.IO) {
                vpnOutput.write(packet.array(), 0, packet.limit())
                vpnOutput.flush()
            }
        } catch (e: Exception) {
            Timber.e(e, "Error forwarding packet")
        }
    }
    
    /**
     * إنشاء إشعار VPN
     */
    private fun createVpnNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, OfflineFirstApplication.CHANNEL_VPN_SERVICE)
            .setContentTitle(getString(R.string.notification_vpn_title))
            .setContentText(getString(R.string.notification_vpn_text))
            .setSmallIcon(R.drawable.ic_vpn)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    /**
     * التحقق من حالة تشغيل VPN
     */
    fun isVpnRunning(): Boolean = isRunning.get()
    
    /**
     * الحصول على إحصائيات VPN
     */
    fun getVpnStatistics(): VpnStatistics {
        return VpnStatistics(
            isRunning = isRunning.get(),
            activeConnections = activeConnections.size,
            totalPacketsProcessed = packetProcessor.getTotalPacketsProcessed(),
            totalBytesProcessed = packetProcessor.getTotalBytesProcessed()
        )
    }
}

/**
 * نموذج بيانات إحصائيات VPN
 */
data class VpnStatistics(
    val isRunning: Boolean,
    val activeConnections: Int,
    val totalPacketsProcessed: Long,
    val totalBytesProcessed: Long
)

/**
 * نموذج بيانات الاتصال
 */
data class VpnConnection(
    val id: String,
    val sourceAddress: String,
    val destinationAddress: String,
    val sourcePort: Int,
    val destinationPort: Int,
    val protocol: PacketProtocol,
    val createdAt: Long = System.currentTimeMillis()
)
