package com.offlinefirst.internetmanager.vpn

import android.content.Context
import android.content.Intent
import android.net.VpnService
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير VPN للتحكم في خدمة VPN المحلي
 */
@Singleton
class VpnManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val _vpnState = MutableStateFlow(VpnState.DISCONNECTED)
    val vpnState: StateFlow<VpnState> = _vpnState.asStateFlow()
    
    private val _vpnStatistics = MutableLiveData<VpnStatistics>()
    val vpnStatistics: LiveData<VpnStatistics> = _vpnStatistics
    
    private val managerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    /**
     * بدء خدمة VPN
     */
    suspend fun startVpnService(): VpnResult {
        return withContext(Dispatchers.Main) {
            try {
                Timber.d("Starting VPN service")
                
                // التحقق من إذن VPN
                val intent = VpnService.prepare(context)
                if (intent != null) {
                    Timber.w("VPN permission required")
                    return@withContext VpnResult.PermissionRequired(intent)
                }
                
                // تحديث الحالة
                _vpnState.value = VpnState.CONNECTING
                
                // بدء الخدمة
                val serviceIntent = Intent(context, LocalVpnService::class.java).apply {
                    action = LocalVpnService.ACTION_START_VPN
                }
                
                context.startForegroundService(serviceIntent)
                
                // انتظار قصير للتأكد من بدء الخدمة
                delay(1000)
                
                // تحديث الحالة
                _vpnState.value = VpnState.CONNECTED
                
                Timber.i("VPN service started successfully")
                VpnResult.Success
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to start VPN service")
                _vpnState.value = VpnState.ERROR
                VpnResult.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * إيقاف خدمة VPN
     */
    suspend fun stopVpnService(): VpnResult {
        return withContext(Dispatchers.Main) {
            try {
                Timber.d("Stopping VPN service")
                
                // تحديث الحالة
                _vpnState.value = VpnState.DISCONNECTING
                
                // إيقاف الخدمة
                val serviceIntent = Intent(context, LocalVpnService::class.java).apply {
                    action = LocalVpnService.ACTION_STOP_VPN
                }
                
                context.stopService(serviceIntent)
                
                // انتظار قصير للتأكد من إيقاف الخدمة
                delay(500)
                
                // تحديث الحالة
                _vpnState.value = VpnState.DISCONNECTED
                
                Timber.i("VPN service stopped successfully")
                VpnResult.Success
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to stop VPN service")
                _vpnState.value = VpnState.ERROR
                VpnResult.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * التحقق من حالة VPN
     */
    fun isVpnRunning(): Boolean {
        return _vpnState.value == VpnState.CONNECTED
    }
    
    /**
     * الحصول على حالة VPN الحالية
     */
    fun getCurrentVpnState(): VpnState {
        return _vpnState.value
    }
    
    /**
     * تحديث إحصائيات VPN
     */
    fun updateVpnStatistics(statistics: VpnStatistics) {
        _vpnStatistics.postValue(statistics)
    }
    
    /**
     * بدء مراقبة حالة VPN
     */
    fun startMonitoring() {
        managerScope.launch {
            while (true) {
                try {
                    // هنا يمكن إضافة منطق مراقبة حالة VPN
                    // مثل التحقق من أن الخدمة ما زالت تعمل
                    
                    delay(5000) // فحص كل 5 ثوانٍ
                } catch (e: Exception) {
                    Timber.e(e, "Error in VPN monitoring")
                }
            }
        }
    }
    
    /**
     * إيقاف مراقبة VPN
     */
    fun stopMonitoring() {
        managerScope.cancel()
    }
}

/**
 * حالات VPN
 */
enum class VpnState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}

/**
 * نتائج عمليات VPN
 */
sealed class VpnResult {
    object Success : VpnResult()
    data class PermissionRequired(val intent: Intent) : VpnResult()
    data class Error(val message: String) : VpnResult()
}

/**
 * معلومات حالة VPN
 */
data class VpnStateInfo(
    val state: VpnState,
    val isRunning: Boolean,
    val connectedSince: Long? = null,
    val lastError: String? = null
) {
    val isConnected: Boolean
        get() = state == VpnState.CONNECTED && isRunning
}
