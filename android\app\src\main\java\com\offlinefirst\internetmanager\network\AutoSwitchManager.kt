package com.offlinefirst.internetmanager.network

import com.offlinefirst.internetmanager.core.NetworkManager
import com.offlinefirst.internetmanager.core.NetworkResult
import com.offlinefirst.internetmanager.data.database.dao.AppSettingsDao
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير التبديل التلقائي بين أوضاع الشبكة
 */
@Singleton
class AutoSwitchManager @Inject constructor(
    private val networkDetector: NetworkDetector,
    private val networkManager: NetworkManager,
    private val appSettingsDao: AppSettingsDao
) {
    
    private val _isAutoSwitchEnabled = MutableStateFlow(true)
    val isAutoSwitchEnabled: StateFlow<Boolean> = _isAutoSwitchEnabled.asStateFlow()
    
    private val _switchHistory = MutableStateFlow<List<SwitchEvent>>(emptyList())
    val switchHistory: StateFlow<List<SwitchEvent>> = _switchHistory.asStateFlow()
    
    private val switchScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var isMonitoring = false
    
    /**
     * بدء مراقبة التبديل التلقائي
     */
    fun startAutoSwitch() {
        if (isMonitoring) {
            Timber.d("Auto switch already monitoring")
            return
        }
        
        switchScope.launch {
            try {
                // مراقبة تغييرات الشبكة
                networkDetector.networkType.collect { networkType ->
                    if (_isAutoSwitchEnabled.value) {
                        handleNetworkTypeChange(networkType)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error in auto switch monitoring")
            }
        }
        
        switchScope.launch {
            try {
                // مراقبة تغييرات حالة الواي-فاي المنزلي
                networkDetector.isHomeWifi.collect { isHomeWifi ->
                    if (_isAutoSwitchEnabled.value) {
                        handleHomeWifiChange(isHomeWifi)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error in home wifi monitoring")
            }
        }
        
        isMonitoring = true
        Timber.i("Auto switch monitoring started")
    }
    
    /**
     * إيقاف مراقبة التبديل التلقائي
     */
    fun stopAutoSwitch() {
        if (!isMonitoring) {
            Timber.d("Auto switch not monitoring")
            return
        }
        
        isMonitoring = false
        Timber.i("Auto switch monitoring stopped")
    }
    
    /**
     * معالجة تغيير نوع الشبكة
     */
    private suspend fun handleNetworkTypeChange(networkType: NetworkType) {
        try {
            val currentNetworkState = networkManager.getCurrentNetworkState()
            
            when (networkType) {
                NetworkType.HOME_WIFI -> {
                    if (!networkManager.isInVpnMode()) {
                        Timber.d("Switching to VPN mode (Home WiFi detected)")
                        switchToVpnMode("Home WiFi connected")
                    }
                }
                
                NetworkType.MOBILE_DATA -> {
                    if (networkManager.isInVpnMode()) {
                        Timber.d("Switching to cache-only mode (Mobile data detected)")
                        switchToCacheOnlyMode("Mobile data connected")
                    }
                }
                
                NetworkType.OTHER_WIFI -> {
                    if (networkManager.isInVpnMode()) {
                        Timber.d("Switching to cache-only mode (Other WiFi detected)")
                        switchToCacheOnlyMode("Other WiFi connected")
                    }
                }
                
                NetworkType.NONE -> {
                    if (networkManager.isInVpnMode()) {
                        Timber.d("Switching to cache-only mode (No network)")
                        switchToCacheOnlyMode("Network disconnected")
                    }
                }
                
                else -> {
                    Timber.d("Unknown network type: $networkType")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error handling network type change: $networkType")
        }
    }
    
    /**
     * معالجة تغيير حالة الواي-فاي المنزلي
     */
    private suspend fun handleHomeWifiChange(isHomeWifi: Boolean) {
        try {
            val currentNetworkType = networkDetector.getCurrentNetworkType()
            
            if (currentNetworkType == NetworkType.HOME_WIFI || 
                currentNetworkType == NetworkType.OTHER_WIFI) {
                
                if (isHomeWifi && !networkManager.isInVpnMode()) {
                    Timber.d("Switching to VPN mode (Home WiFi confirmed)")
                    switchToVpnMode("Home WiFi confirmed")
                } else if (!isHomeWifi && networkManager.isInVpnMode()) {
                    Timber.d("Switching to cache-only mode (Not home WiFi)")
                    switchToCacheOnlyMode("Not home WiFi")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error handling home wifi change: $isHomeWifi")
        }
    }
    
    /**
     * التبديل لوضع VPN
     */
    private suspend fun switchToVpnMode(reason: String) {
        try {
            val result = networkManager.switchToVpnMode()
            
            when (result) {
                is NetworkResult.Success -> {
                    addSwitchEvent(SwitchEvent(
                        fromMode = SwitchMode.CACHE_ONLY,
                        toMode = SwitchMode.VPN,
                        reason = reason,
                        success = true
                    ))
                    Timber.i("Successfully switched to VPN mode: $reason")
                }
                
                is NetworkResult.PermissionRequired -> {
                    addSwitchEvent(SwitchEvent(
                        fromMode = SwitchMode.CACHE_ONLY,
                        toMode = SwitchMode.VPN,
                        reason = reason,
                        success = false,
                        error = "VPN permission required"
                    ))
                    Timber.w("VPN permission required for switch: $reason")
                }
                
                is NetworkResult.Error -> {
                    addSwitchEvent(SwitchEvent(
                        fromMode = SwitchMode.CACHE_ONLY,
                        toMode = SwitchMode.VPN,
                        reason = reason,
                        success = false,
                        error = result.message
                    ))
                    Timber.e("Failed to switch to VPN mode: ${result.message}")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error switching to VPN mode")
            addSwitchEvent(SwitchEvent(
                fromMode = SwitchMode.CACHE_ONLY,
                toMode = SwitchMode.VPN,
                reason = reason,
                success = false,
                error = e.message ?: "Unknown error"
            ))
        }
    }
    
    /**
     * التبديل لوضع الكاش فقط
     */
    private suspend fun switchToCacheOnlyMode(reason: String) {
        try {
            val result = networkManager.switchToCacheOnlyMode()
            
            when (result) {
                is NetworkResult.Success -> {
                    addSwitchEvent(SwitchEvent(
                        fromMode = SwitchMode.VPN,
                        toMode = SwitchMode.CACHE_ONLY,
                        reason = reason,
                        success = true
                    ))
                    Timber.i("Successfully switched to cache-only mode: $reason")
                }
                
                is NetworkResult.Error -> {
                    addSwitchEvent(SwitchEvent(
                        fromMode = SwitchMode.VPN,
                        toMode = SwitchMode.CACHE_ONLY,
                        reason = reason,
                        success = false,
                        error = result.message
                    ))
                    Timber.e("Failed to switch to cache-only mode: ${result.message}")
                }
                
                else -> {
                    Timber.w("Unexpected result switching to cache-only mode: $result")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error switching to cache-only mode")
            addSwitchEvent(SwitchEvent(
                fromMode = SwitchMode.VPN,
                toMode = SwitchMode.CACHE_ONLY,
                reason = reason,
                success = false,
                error = e.message ?: "Unknown error"
            ))
        }
    }
    
    /**
     * إضافة حدث تبديل لسجل التاريخ
     */
    private fun addSwitchEvent(event: SwitchEvent) {
        val currentHistory = _switchHistory.value.toMutableList()
        currentHistory.add(0, event) // إضافة في المقدمة
        
        // الاحتفاظ بآخر 50 حدث فقط
        if (currentHistory.size > 50) {
            currentHistory.removeAt(currentHistory.size - 1)
        }
        
        _switchHistory.value = currentHistory
    }
    
    /**
     * تفعيل/إلغاء تفعيل التبديل التلقائي
     */
    suspend fun setAutoSwitchEnabled(enabled: Boolean) {
        _isAutoSwitchEnabled.value = enabled
        
        // حفظ الإعداد في قاعدة البيانات
        try {
            val settings = appSettingsDao.getSettings()
            if (settings != null) {
                appSettingsDao.updateSettings(settings.copy(enableVpn = enabled))
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to save auto switch setting")
        }
        
        if (enabled) {
            Timber.i("Auto switch enabled")
            // فحص الشبكة الحالية وتطبيق التبديل المناسب
            val currentNetworkType = networkDetector.getCurrentNetworkType()
            handleNetworkTypeChange(currentNetworkType)
        } else {
            Timber.i("Auto switch disabled")
        }
    }
    
    /**
     * التبديل اليدوي
     */
    suspend fun manualSwitch(targetMode: SwitchMode): NetworkResult {
        return try {
            when (targetMode) {
                SwitchMode.VPN -> {
                    switchToVpnMode("Manual switch")
                    NetworkResult.Success
                }
                SwitchMode.CACHE_ONLY -> {
                    switchToCacheOnlyMode("Manual switch")
                    NetworkResult.Success
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in manual switch to $targetMode")
            NetworkResult.Error(e.message ?: "Unknown error")
        }
    }
    
    /**
     * مسح سجل التبديل
     */
    fun clearSwitchHistory() {
        _switchHistory.value = emptyList()
        Timber.d("Switch history cleared")
    }
    
    /**
     * الحصول على إحصائيات التبديل
     */
    fun getSwitchStatistics(): SwitchStatistics {
        val history = _switchHistory.value
        val successfulSwitches = history.count { it.success }
        val failedSwitches = history.count { !it.success }
        val vpnSwitches = history.count { it.toMode == SwitchMode.VPN }
        val cacheSwitches = history.count { it.toMode == SwitchMode.CACHE_ONLY }
        
        return SwitchStatistics(
            totalSwitches = history.size,
            successfulSwitches = successfulSwitches,
            failedSwitches = failedSwitches,
            vpnSwitches = vpnSwitches,
            cacheSwitches = cacheSwitches,
            successRate = if (history.isNotEmpty()) {
                (successfulSwitches.toFloat() / history.size * 100)
            } else 0f
        )
    }
    
    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        stopAutoSwitch()
        switchScope.cancel()
    }
}

/**
 * أوضاع التبديل
 */
enum class SwitchMode {
    VPN,
    CACHE_ONLY
}

/**
 * حدث التبديل
 */
data class SwitchEvent(
    val fromMode: SwitchMode,
    val toMode: SwitchMode,
    val reason: String,
    val success: Boolean,
    val error: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * إحصائيات التبديل
 */
data class SwitchStatistics(
    val totalSwitches: Int,
    val successfulSwitches: Int,
    val failedSwitches: Int,
    val vpnSwitches: Int,
    val cacheSwitches: Int,
    val successRate: Float
)
