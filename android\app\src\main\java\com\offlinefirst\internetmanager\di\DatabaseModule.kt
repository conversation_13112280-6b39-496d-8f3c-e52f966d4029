package com.offlinefirst.internetmanager.di

import android.content.Context
import androidx.room.Room
import com.offlinefirst.internetmanager.data.database.AppDatabase
import com.offlinefirst.internetmanager.data.database.dao.AppSettingsDao
import com.offlinefirst.internetmanager.data.database.dao.CacheDao
import com.offlinefirst.internetmanager.data.database.dao.NetworkStatsDao
import com.offlinefirst.internetmanager.data.database.dao.PrefetchResourceDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * وحدة Hilt لقاعدة البيانات
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    /**
     * توفير قاعدة البيانات
     */
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            AppDatabase::class.java,
            AppDatabase.DATABASE_NAME
        )
        .fallbackToDestructiveMigration() // للتطوير فقط
        .build()
    }
    
    /**
     * توفير CacheDao
     */
    @Provides
    fun provideCacheDao(database: AppDatabase): CacheDao {
        return database.cacheDao()
    }
    
    /**
     * توفير PrefetchResourceDao
     */
    @Provides
    fun providePrefetchResourceDao(database: AppDatabase): PrefetchResourceDao {
        return database.prefetchResourceDao()
    }
    
    /**
     * توفير NetworkStatsDao
     */
    @Provides
    fun provideNetworkStatsDao(database: AppDatabase): NetworkStatsDao {
        return database.networkStatsDao()
    }
    
    /**
     * توفير AppSettingsDao
     */
    @Provides
    fun provideAppSettingsDao(database: AppDatabase): AppSettingsDao {
        return database.appSettingsDao()
    }
}
