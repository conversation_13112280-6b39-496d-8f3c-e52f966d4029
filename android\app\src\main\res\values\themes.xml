<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.OfflineFirstInternetManager" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background_light</item>
    </style>
    
    <!-- Theme for VPN notification -->
    <style name="Theme.VpnNotification" parent="Theme.Material3.DayNight">
        <item name="colorPrimary">@color/status_connected</item>
    </style>
</resources>
