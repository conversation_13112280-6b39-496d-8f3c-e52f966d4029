package com.offlinefirst.internetmanager.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.offlinefirst.internetmanager.data.model.AppSettings

/**
 * DAO للتعامل مع إعدادات التطبيق
 */
@Dao
interface AppSettingsDao {
    
    /**
     * الحصول على الإعدادات الافتراضية
     */
    @Query("SELECT * FROM app_settings WHERE id = 'default' LIMIT 1")
    suspend fun getSettings(): AppSettings?
    
    /**
     * إدراج أو تحديث الإعدادات
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: AppSettings)
    
    /**
     * تحديث الإعدادات
     */
    @Update
    suspend fun updateSettings(settings: AppSettings)
    
    /**
     * حذف الإعدادات
     */
    @Query("DELETE FROM app_settings WHERE id = 'default'")
    suspend fun deleteSettings()
    
    /**
     * تحديث قائمة SSID الشبكات المنزلية
     */
    @Query("UPDATE app_settings SET homeWifiSSIDs = :ssids WHERE id = 'default'")
    suspend fun updateHomeWifiSSIDs(ssids: List<String>)
    
    /**
     * تحديث الحد الأقصى لحجم الكاش
     */
    @Query("UPDATE app_settings SET maxCacheSize = :size WHERE id = 'default'")
    suspend fun updateMaxCacheSize(size: Long)
    
    /**
     * تحديث فترة Prefetch
     */
    @Query("UPDATE app_settings SET prefetchIntervalHours = :hours WHERE id = 'default'")
    suspend fun updatePrefetchInterval(hours: Int)
    
    /**
     * تحديث مدة انتهاء صلاحية الكاش
     */
    @Query("UPDATE app_settings SET cacheExpiryHours = :hours WHERE id = 'default'")
    suspend fun updateCacheExpiry(hours: Int)
    
    /**
     * تفعيل/إلغاء تفعيل Prefetch
     */
    @Query("UPDATE app_settings SET enablePrefetch = :enabled WHERE id = 'default'")
    suspend fun setEnablePrefetch(enabled: Boolean)
    
    /**
     * تفعيل/إلغاء تفعيل VPN
     */
    @Query("UPDATE app_settings SET enableVpn = :enabled WHERE id = 'default'")
    suspend fun setEnableVpn(enabled: Boolean)
    
    /**
     * تفعيل/إلغاء تفعيل Prefetch على الواي-فاي فقط
     */
    @Query("UPDATE app_settings SET prefetchOnlyOnWifi = :enabled WHERE id = 'default'")
    suspend fun setPrefetchOnlyOnWifi(enabled: Boolean)
    
    /**
     * تفعيل/إلغاء تفعيل Prefetch عند الشحن فقط
     */
    @Query("UPDATE app_settings SET prefetchOnlyWhenCharging = :enabled WHERE id = 'default'")
    suspend fun setPrefetchOnlyWhenCharging(enabled: Boolean)
    
    /**
     * تفعيل/إلغاء تفعيل ضغط البيانات
     */
    @Query("UPDATE app_settings SET enableCompression = :enabled WHERE id = 'default'")
    suspend fun setEnableCompression(enabled: Boolean)
    
    /**
     * تفعيل/إلغاء تفعيل الإشعارات
     */
    @Query("UPDATE app_settings SET enableNotifications = :enabled WHERE id = 'default'")
    suspend fun setEnableNotifications(enabled: Boolean)
    
    /**
     * تفعيل/إلغاء تفعيل السجلات المفصلة
     */
    @Query("UPDATE app_settings SET enableVerboseLogging = :enabled WHERE id = 'default'")
    suspend fun setEnableVerboseLogging(enabled: Boolean)
    
    /**
     * مراقبة الإعدادات
     */
    @Query("SELECT * FROM app_settings WHERE id = 'default' LIMIT 1")
    fun observeSettings(): Flow<AppSettings?>
    
    /**
     * مراقبة قائمة SSID الشبكات المنزلية
     */
    @Query("SELECT homeWifiSSIDs FROM app_settings WHERE id = 'default' LIMIT 1")
    fun observeHomeWifiSSIDs(): Flow<List<String>?>
    
    /**
     * مراقبة حالة تفعيل VPN
     */
    @Query("SELECT enableVpn FROM app_settings WHERE id = 'default' LIMIT 1")
    fun observeVpnEnabled(): Flow<Boolean?>
    
    /**
     * مراقبة حالة تفعيل Prefetch
     */
    @Query("SELECT enablePrefetch FROM app_settings WHERE id = 'default' LIMIT 1")
    fun observePrefetchEnabled(): Flow<Boolean?>
}
