package com.offlinefirst.internetmanager.cache

import android.content.Context
import com.offlinefirst.internetmanager.data.database.dao.AppSettingsDao
import com.offlinefirst.internetmanager.data.database.dao.CacheDao
import com.offlinefirst.internetmanager.data.database.dao.NetworkStatsDao
import com.offlinefirst.internetmanager.data.model.AppSettings
import com.offlinefirst.internetmanager.data.model.CacheEntry
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import okhttp3.*
import okio.Buffer
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.security.MessageDigest
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير الكاش الذكي للتطبيق
 */
@Singleton
class CacheManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheDao: CacheDao,
    private val networkStatsDao: NetworkStatsDao,
    private val appSettingsDao: AppSettingsDao,
    private val okHttpClient: OkHttpClient
) {
    
    private val cacheScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _cacheStats = MutableStateFlow(CacheStats())
    val cacheStats: StateFlow<CacheStats> = _cacheStats.asStateFlow()
    
    // مجلد الكاش الرئيسي
    private val cacheDirectory = File(context.cacheDir, "offline_cache")
    
    init {
        // إنشاء مجلد الكاش إذا لم يكن موجوداً
        if (!cacheDirectory.exists()) {
            cacheDirectory.mkdirs()
        }
        
        // بدء مراقبة إحصائيات الكاش
        startCacheStatsMonitoring()
        
        // بدء تنظيف الكاش الدوري
        startPeriodicCacheCleanup()
    }
    
    /**
     * جلب مورد من الكاش أو الشبكة
     */
    suspend fun fetchResource(url: String): CacheResult {
        return withContext(Dispatchers.IO) {
            try {
                // البحث في الكاش أولاً
                val cachedEntry = cacheDao.getCachedResponse(url)
                
                if (cachedEntry != null && !cachedEntry.isExpired()) {
                    Timber.d("Cache HIT for: $url")
                    
                    // تحديث إحصائيات الوصول
                    cacheDao.updateAccessStats(url)
                    networkStatsDao.incrementCacheHit()
                    networkStatsDao.addDataSaved(cachedEntry.size)
                    
                    // قراءة البيانات من الملف
                    val cacheFile = File(cachedEntry.filePath ?: "")
                    if (cacheFile.exists()) {
                        CacheResult.Hit(
                            data = cacheFile.readBytes(),
                            contentType = cachedEntry.contentType,
                            timestamp = cachedEntry.timestamp,
                            size = cachedEntry.size
                        )
                    } else {
                        // الملف غير موجود، حذف الإدخال من قاعدة البيانات
                        cacheDao.deleteCacheEntry(cachedEntry)
                        fetchFromNetwork(url)
                    }
                } else {
                    Timber.d("Cache MISS for: $url")
                    networkStatsDao.incrementCacheMiss()
                    
                    // حذف الإدخال المنتهي الصلاحية
                    if (cachedEntry != null) {
                        cacheDao.deleteCacheEntry(cachedEntry)
                        deleteCacheFile(cachedEntry.filePath)
                    }
                    
                    fetchFromNetwork(url)
                }
            } catch (e: Exception) {
                Timber.e(e, "Error fetching resource: $url")
                CacheResult.Error(e)
            }
        }
    }
    
    /**
     * جلب مورد من الشبكة وحفظه في الكاش
     */
    private suspend fun fetchFromNetwork(url: String): CacheResult {
        return try {
            val request = Request.Builder().url(url).build()
            val response = okHttpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val responseBody = response.body
                if (responseBody != null) {
                    val data = responseBody.bytes()
                    
                    // حفظ في الكاش
                    saveToCache(url, response, data)
                    
                    // تحديث الإحصائيات
                    networkStatsDao.addDataUsed(data.size.toLong())
                    
                    CacheResult.NetworkHit(
                        data = data,
                        contentType = response.header("Content-Type"),
                        timestamp = System.currentTimeMillis(),
                        size = data.size.toLong()
                    )
                } else {
                    CacheResult.Error(IOException("Empty response body"))
                }
            } else {
                CacheResult.Error(IOException("HTTP ${response.code}: ${response.message}"))
            }
        } catch (e: Exception) {
            Timber.e(e, "Network request failed: $url")
            CacheResult.Error(e)
        }
    }
    
    /**
     * حفظ البيانات في الكاش
     */
    private suspend fun saveToCache(url: String, response: Response, data: ByteArray) {
        try {
            val settings = appSettingsDao.getSettings() ?: AppSettings()
            
            // التحقق من الحد الأقصى لحجم الكاش
            val currentCacheSize = cacheDao.getTotalCacheSize()
            if (currentCacheSize + data.size > settings.maxCacheSize) {
                // تنظيف الكاش لإفساح المجال
                cleanupCacheSpace(data.size.toLong())
            }
            
            // إنشاء ملف الكاش
            val fileName = generateCacheFileName(url)
            val cacheFile = File(cacheDirectory, fileName)
            cacheFile.writeBytes(data)
            
            // إنشاء إدخال قاعدة البيانات
            val cacheEntry = CacheEntry(
                url = url,
                size = data.size.toLong(),
                contentType = response.header("Content-Type"),
                etag = response.header("ETag"),
                lastModified = response.header("Last-Modified"),
                expiryTime = calculateExpiryTime(response, settings),
                filePath = cacheFile.absolutePath,
                responseCode = response.code,
                headers = serializeHeaders(response.headers)
            )
            
            cacheDao.insertCacheEntry(cacheEntry)
            
            Timber.d("Saved to cache: $url (${data.size} bytes)")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to save to cache: $url")
        }
    }
    
    /**
     * مسح الكاش بالكامل
     */
    suspend fun clearCache(): Long {
        return withContext(Dispatchers.IO) {
            try {
                val totalSize = cacheDao.getTotalCacheSize()
                
                // حذف جميع الملفات
                cacheDirectory.listFiles()?.forEach { file ->
                    if (file.isFile) {
                        file.delete()
                    }
                }
                
                // حذف جميع الإدخالات من قاعدة البيانات
                cacheDao.deleteAllEntries()
                
                Timber.i("Cache cleared: $totalSize bytes freed")
                totalSize
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear cache")
                0L
            }
        }
    }
    
    /**
     * تنظيف مساحة الكاش
     */
    private suspend fun cleanupCacheSpace(requiredSpace: Long) {
        try {
            val currentSize = cacheDao.getTotalCacheSize()
            val settings = appSettingsDao.getSettings() ?: AppSettings()
            val targetSize = settings.maxCacheSize - requiredSpace
            
            if (currentSize <= targetSize) return
            
            // حذف الإدخالات الأقل استخداماً
            val entriesToDelete = cacheDao.getLeastAccessedEntries(50)
            var freedSpace = 0L
            
            for (entry in entriesToDelete) {
                if (freedSpace >= (currentSize - targetSize)) break
                
                deleteCacheEntry(entry)
                freedSpace += entry.size
            }
            
            Timber.d("Cleaned up cache: $freedSpace bytes freed")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup cache space")
        }
    }
    
    /**
     * حذف إدخال كاش
     */
    private suspend fun deleteCacheEntry(entry: CacheEntry) {
        try {
            // حذف الملف
            deleteCacheFile(entry.filePath)
            
            // حذف من قاعدة البيانات
            cacheDao.deleteCacheEntry(entry)
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete cache entry: ${entry.url}")
        }
    }
    
    /**
     * حذف ملف الكاش
     */
    private fun deleteCacheFile(filePath: String?) {
        try {
            if (filePath != null) {
                val file = File(filePath)
                if (file.exists()) {
                    file.delete()
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to delete cache file: $filePath")
        }
    }
    
    /**
     * حساب وقت انتهاء الصلاحية
     */
    private fun calculateExpiryTime(response: Response, settings: AppSettings): Long? {
        // البحث عن Cache-Control max-age
        val cacheControl = response.header("Cache-Control")
        if (cacheControl != null) {
            val maxAgeRegex = "max-age=(\\d+)".toRegex()
            val matchResult = maxAgeRegex.find(cacheControl)
            if (matchResult != null) {
                val maxAge = matchResult.groupValues[1].toLongOrNull()
                if (maxAge != null) {
                    return System.currentTimeMillis() + (maxAge * 1000)
                }
            }
        }
        
        // استخدام إعدادات التطبيق
        return System.currentTimeMillis() + (settings.cacheExpiryHours * 60 * 60 * 1000)
    }
    
    /**
     * إنشاء اسم ملف الكاش
     */
    private fun generateCacheFileName(url: String): String {
        val md5 = MessageDigest.getInstance("MD5")
        val hash = md5.digest(url.toByteArray())
        return hash.joinToString("") { "%02x".format(it) } + ".cache"
    }
    
    /**
     * تسلسل رؤوس HTTP
     */
    private fun serializeHeaders(headers: Headers): String {
        return headers.toString()
    }
    
    /**
     * بدء مراقبة إحصائيات الكاش
     */
    private fun startCacheStatsMonitoring() {
        cacheScope.launch {
            while (true) {
                try {
                    updateCacheStats()
                    delay(5000) // تحديث كل 5 ثوانٍ
                } catch (e: Exception) {
                    Timber.e(e, "Error updating cache stats")
                }
            }
        }
    }
    
    /**
     * تحديث إحصائيات الكاش
     */
    private suspend fun updateCacheStats() {
        try {
            val statistics = cacheDao.getCacheStatistics()
            val stats = CacheStats(
                totalSize = statistics.totalSize,
                entryCount = statistics.totalEntries,
                hitCount = 0, // سيتم حسابها من NetworkStats
                missCount = 0, // سيتم حسابها من NetworkStats
                oldestEntry = statistics.oldestTimestamp,
                newestEntry = statistics.newestTimestamp
            )
            
            _cacheStats.value = stats
        } catch (e: Exception) {
            Timber.e(e, "Failed to update cache stats")
        }
    }
    
    /**
     * بدء تنظيف الكاش الدوري
     */
    private fun startPeriodicCacheCleanup() {
        cacheScope.launch {
            while (true) {
                try {
                    // تنظيف الإدخالات المنتهية الصلاحية
                    cacheDao.deleteExpiredEntries()
                    
                    // تنظيف الملفات اليتيمة (بدون إدخالات في قاعدة البيانات)
                    cleanupOrphanedFiles()
                    
                    delay(TimeUnit.HOURS.toMillis(1)) // تنظيف كل ساعة
                } catch (e: Exception) {
                    Timber.e(e, "Error in periodic cache cleanup")
                }
            }
        }
    }
    
    /**
     * تنظيف الملفات اليتيمة
     */
    private suspend fun cleanupOrphanedFiles() {
        try {
            val cacheFiles = cacheDirectory.listFiles() ?: return
            
            for (file in cacheFiles) {
                if (file.isFile) {
                    // البحث عن إدخال في قاعدة البيانات
                    val entry = cacheDao.getCachedResponse(file.absolutePath)
                    if (entry == null) {
                        // ملف يتيم، حذفه
                        file.delete()
                        Timber.d("Deleted orphaned cache file: ${file.name}")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to cleanup orphaned files")
        }
    }
    
    /**
     * الحصول على إحصائيات الكاش
     */
    fun getCacheStatsFlow(): Flow<CacheStats> = cacheStats
}

/**
 * نتائج عمليات الكاش
 */
sealed class CacheResult {
    data class Hit(
        val data: ByteArray,
        val contentType: String?,
        val timestamp: Long,
        val size: Long
    ) : CacheResult()
    
    data class NetworkHit(
        val data: ByteArray,
        val contentType: String?,
        val timestamp: Long,
        val size: Long
    ) : CacheResult()
    
    data class Error(val exception: Exception) : CacheResult()
}

/**
 * إحصائيات الكاش
 */
data class CacheStats(
    val totalSize: Long = 0,
    val entryCount: Int = 0,
    val hitCount: Long = 0,
    val missCount: Long = 0,
    val oldestEntry: Long? = null,
    val newestEntry: Long? = null
) {
    val hitRatio: Float
        get() = if (hitCount + missCount > 0) {
            hitCount.toFloat() / (hitCount + missCount) * 100
        } else 0f
}
