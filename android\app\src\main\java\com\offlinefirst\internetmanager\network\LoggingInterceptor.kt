package com.offlinefirst.internetmanager.network

import okhttp3.Interceptor
import okhttp3.Response
import okio.Buffer
import timber.log.Timber
import java.io.IOException
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

/**
 * معترض السجلات المخصص لـ OkHttp
 */
class LoggingInterceptor : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val startTime = System.nanoTime()
        
        // تسجيل الطلب
        logRequest(request)
        
        val response = try {
            chain.proceed(request)
        } catch (e: Exception) {
            Timber.e(e, "HTTP request failed: ${request.url}")
            throw e
        }
        
        val endTime = System.nanoTime()
        val duration = (endTime - startTime) / 1_000_000 // تحويل إلى ميلي ثانية
        
        // تسجيل الاستجابة
        logResponse(response, duration)
        
        return response
    }
    
    /**
     * تسجيل تفاصيل الطلب
     */
    private fun logRequest(request: okhttp3.Request) {
        try {
            val url = request.url
            val method = request.method
            val headers = request.headers
            
            Timber.d("→ $method $url")
            
            // تسجيل الرؤوس المهمة
            if (headers.size > 0) {
                for (i in 0 until headers.size) {
                    val name = headers.name(i)
                    val value = headers.value(i)
                    
                    // تجنب تسجيل الرؤوس الحساسة
                    if (!isSensitiveHeader(name)) {
                        Timber.v("  $name: $value")
                    }
                }
            }
            
            // تسجيل محتوى الطلب إذا كان موجوداً
            val requestBody = request.body
            if (requestBody != null) {
                val contentType = requestBody.contentType()
                val contentLength = requestBody.contentLength()
                
                Timber.v("  Content-Type: $contentType")
                Timber.v("  Content-Length: $contentLength")
                
                // تسجيل محتوى الطلب للأنواع النصية فقط
                if (isTextContentType(contentType?.toString())) {
                    val buffer = Buffer()
                    requestBody.writeTo(buffer)
                    
                    val charset = contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8
                    val content = buffer.readString(charset)
                    
                    if (content.length <= MAX_LOG_CONTENT_LENGTH) {
                        Timber.v("  Body: $content")
                    } else {
                        Timber.v("  Body: ${content.substring(0, MAX_LOG_CONTENT_LENGTH)}... (truncated)")
                    }
                }
            }
            
        } catch (e: Exception) {
            Timber.w(e, "Failed to log request details")
        }
    }
    
    /**
     * تسجيل تفاصيل الاستجابة
     */
    private fun logResponse(response: Response, duration: Long) {
        try {
            val request = response.request
            val url = request.url
            val code = response.code
            val message = response.message
            val headers = response.headers
            
            Timber.d("← $code $message $url (${duration}ms)")
            
            // تسجيل الرؤوس المهمة
            val importantHeaders = listOf(
                "Content-Type",
                "Content-Length", 
                "Cache-Control",
                "ETag",
                "Last-Modified",
                "Expires"
            )
            
            for (headerName in importantHeaders) {
                val headerValue = headers[headerName]
                if (headerValue != null) {
                    Timber.v("  $headerName: $headerValue")
                }
            }
            
            // تسجيل محتوى الاستجابة للأنواع النصية فقط
            val responseBody = response.body
            if (responseBody != null) {
                val contentType = responseBody.contentType()
                val contentLength = responseBody.contentLength()
                
                if (isTextContentType(contentType?.toString()) && contentLength <= MAX_LOG_CONTENT_LENGTH) {
                    val source = responseBody.source()
                    source.request(Long.MAX_VALUE)
                    val buffer = source.buffer
                    
                    val charset = contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8
                    val content = buffer.clone().readString(charset)
                    
                    if (content.length <= MAX_LOG_CONTENT_LENGTH) {
                        Timber.v("  Response Body: $content")
                    } else {
                        Timber.v("  Response Body: ${content.substring(0, MAX_LOG_CONTENT_LENGTH)}... (truncated)")
                    }
                }
            }
            
        } catch (e: Exception) {
            Timber.w(e, "Failed to log response details")
        }
    }
    
    /**
     * التحقق من أن الرأس حساس ولا يجب تسجيله
     */
    private fun isSensitiveHeader(headerName: String): Boolean {
        val sensitiveHeaders = setOf(
            "authorization",
            "cookie",
            "set-cookie",
            "x-api-key",
            "x-auth-token",
            "bearer"
        )
        
        return sensitiveHeaders.contains(headerName.lowercase())
    }
    
    /**
     * التحقق من أن نوع المحتوى نصي
     */
    private fun isTextContentType(contentType: String?): Boolean {
        if (contentType == null) return false
        
        val textTypes = listOf(
            "text/",
            "application/json",
            "application/xml",
            "application/x-www-form-urlencoded"
        )
        
        return textTypes.any { contentType.startsWith(it, ignoreCase = true) }
    }
    
    companion object {
        private const val MAX_LOG_CONTENT_LENGTH = 1000 // الحد الأقصى لطول المحتوى المسجل
    }
}
