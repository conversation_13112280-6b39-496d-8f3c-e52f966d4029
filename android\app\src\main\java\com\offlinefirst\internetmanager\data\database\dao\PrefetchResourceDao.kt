package com.offlinefirst.internetmanager.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.offlinefirst.internetmanager.data.model.PrefetchResource

/**
 * DAO للتعامل مع موارد Prefetch
 */
@Dao
interface PrefetchResourceDao {
    
    /**
     * الحصول على جميع موارد Prefetch
     */
    @Query("SELECT * FROM prefetch_resources ORDER BY priority DESC, createdAt ASC")
    suspend fun getAllResources(): List<PrefetchResource>
    
    /**
     * الحصول على الموارد المفعلة فقط
     */
    @Query("SELECT * FROM prefetch_resources WHERE isEnabled = 1 ORDER BY priority DESC")
    suspend fun getEnabledResources(): List<PrefetchResource>
    
    /**
     * الحصول على الموارد التي تحتاج إعادة جلب
     */
    @Query("""
        SELECT * FROM prefetch_resources 
        WHERE isEnabled = 1 
        AND (lastFetched IS NULL OR 
             (strftime('%s', 'now') * 1000 - lastFetched) > (fetchIntervalHours * 3600000))
        ORDER BY priority DESC
    """)
    suspend fun getResourcesNeedingRefetch(): List<PrefetchResource>
    
    /**
     * الحصول على مورد بالرابط
     */
    @Query("SELECT * FROM prefetch_resources WHERE url = :url LIMIT 1")
    suspend fun getResourceByUrl(url: String): PrefetchResource?
    
    /**
     * الحصول على مورد بالمعرف
     */
    @Query("SELECT * FROM prefetch_resources WHERE id = :id LIMIT 1")
    suspend fun getResourceById(id: String): PrefetchResource?
    
    /**
     * الحصول على الموارد حسب الفئة
     */
    @Query("SELECT * FROM prefetch_resources WHERE category = :category ORDER BY priority DESC")
    suspend fun getResourcesByCategory(category: String): List<PrefetchResource>
    
    /**
     * إدراج مورد جديد
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertResource(resource: PrefetchResource)
    
    /**
     * إدراج عدة موارد
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertResources(resources: List<PrefetchResource>)
    
    /**
     * تحديث مورد
     */
    @Update
    suspend fun updateResource(resource: PrefetchResource)
    
    /**
     * حذف مورد
     */
    @Delete
    suspend fun deleteResource(resource: PrefetchResource)
    
    /**
     * حذف مورد بالمعرف
     */
    @Query("DELETE FROM prefetch_resources WHERE id = :id")
    suspend fun deleteResourceById(id: String)
    
    /**
     * حذف مورد بالرابط
     */
    @Query("DELETE FROM prefetch_resources WHERE url = :url")
    suspend fun deleteResourceByUrl(url: String)
    
    /**
     * حذف جميع الموارد
     */
    @Query("DELETE FROM prefetch_resources")
    suspend fun deleteAllResources()
    
    /**
     * تفعيل/إلغاء تفعيل مورد
     */
    @Query("UPDATE prefetch_resources SET isEnabled = :enabled, updatedAt = :updatedAt WHERE id = :id")
    suspend fun setResourceEnabled(id: String, enabled: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * تحديث أولوية مورد
     */
    @Query("UPDATE prefetch_resources SET priority = :priority, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateResourcePriority(id: String, priority: Int, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * تحديث إحصائيات الجلب الناجح
     */
    @Query("""
        UPDATE prefetch_resources 
        SET lastFetched = :fetchTime, 
            fetchCount = fetchCount + 1, 
            lastError = NULL,
            updatedAt = :fetchTime
        WHERE id = :id
    """)
    suspend fun updateSuccessfulFetch(id: String, fetchTime: Long = System.currentTimeMillis())
    
    /**
     * تحديث إحصائيات الجلب الفاشل
     */
    @Query("""
        UPDATE prefetch_resources 
        SET failureCount = failureCount + 1, 
            lastError = :error,
            updatedAt = :updateTime
        WHERE id = :id
    """)
    suspend fun updateFailedFetch(id: String, error: String, updateTime: Long = System.currentTimeMillis())
    
    /**
     * الحصول على عدد الموارد المفعلة
     */
    @Query("SELECT COUNT(*) FROM prefetch_resources WHERE isEnabled = 1")
    suspend fun getEnabledResourceCount(): Int
    
    /**
     * الحصول على عدد الموارد حسب الفئة
     */
    @Query("SELECT COUNT(*) FROM prefetch_resources WHERE category = :category")
    suspend fun getResourceCountByCategory(category: String): Int
    
    /**
     * البحث في الموارد
     */
    @Query("SELECT * FROM prefetch_resources WHERE url LIKE '%' || :searchTerm || '%' OR category LIKE '%' || :searchTerm || '%'")
    suspend fun searchResources(searchTerm: String): List<PrefetchResource>
    
    /**
     * الحصول على الموارد الأكثر فشلاً
     */
    @Query("SELECT * FROM prefetch_resources WHERE failureCount > 0 ORDER BY failureCount DESC LIMIT :limit")
    suspend fun getMostFailedResources(limit: Int = 10): List<PrefetchResource>
    
    /**
     * الحصول على الموارد الأكثر نجاحاً
     */
    @Query("SELECT * FROM prefetch_resources WHERE fetchCount > 0 ORDER BY fetchCount DESC LIMIT :limit")
    suspend fun getMostSuccessfulResources(limit: Int = 10): List<PrefetchResource>
    
    /**
     * مراقبة جميع الموارد
     */
    @Query("SELECT * FROM prefetch_resources ORDER BY priority DESC, createdAt ASC")
    fun observeAllResources(): Flow<List<PrefetchResource>>
    
    /**
     * مراقبة الموارد المفعلة
     */
    @Query("SELECT * FROM prefetch_resources WHERE isEnabled = 1 ORDER BY priority DESC")
    fun observeEnabledResources(): Flow<List<PrefetchResource>>
    
    /**
     * مراقبة عدد الموارد المفعلة
     */
    @Query("SELECT COUNT(*) FROM prefetch_resources WHERE isEnabled = 1")
    fun observeEnabledResourceCount(): Flow<Int>
    
    /**
     * الحصول على إحصائيات Prefetch
     */
    @Query("""
        SELECT 
            COUNT(*) as totalResources,
            SUM(CASE WHEN isEnabled = 1 THEN 1 ELSE 0 END) as enabledResources,
            SUM(fetchCount) as totalFetches,
            SUM(failureCount) as totalFailures,
            COUNT(CASE WHEN lastFetched IS NOT NULL THEN 1 END) as fetchedResources
        FROM prefetch_resources
    """)
    suspend fun getPrefetchStatistics(): PrefetchStatistics
}

/**
 * نموذج بيانات إحصائيات Prefetch
 */
data class PrefetchStatistics(
    val totalResources: Int,
    val enabledResources: Int,
    val totalFetches: Int,
    val totalFailures: Int,
    val fetchedResources: Int
) {
    val successRate: Float
        get() = if (totalFetches + totalFailures > 0) {
            totalFetches.toFloat() / (totalFetches + totalFailures) * 100
        } else 0f
}
