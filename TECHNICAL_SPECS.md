# المواصفات التقنية المفصلة
## Technical Specifications

## 🏗️ معمارية النظام

### 1. طبقة كشف الشبكة (Network Detection Layer)

#### Android Implementation
```kotlin
class NetworkDetector(private val context: Context) {
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            val networkInfo = connectivityManager.getNetworkInfo(network)
            if (isHomeWifi(network)) {
                startVpnService()
                enqueuePrefetch()
            } else {
                stopVpnService()
            }
        }
        
        override fun onLost(network: Network) {
            stopVpnService()
        }
    }
    
    fun startMonitoring() {
        val request = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            .build()
        connectivityManager.registerNetworkCallback(request, networkCallback)
    }
}
```

#### iOS Implementation
```swift
import Network

class NetworkDetector {
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    func startMonitoring() {
        monitor.pathUpdateHandler = { path in
            if path.status == .satisfied {
                if self.isHomeWifi(path) {
                    self.startVpnService()
                    self.enqueuePrefetch()
                } else {
                    self.stopVpnService()
                }
            } else {
                self.stopVpnService()
            }
        }
        monitor.start(queue: queue)
    }
}
```

### 2. خدمة VPN المحلي (Local VPN Service)

#### Android VpnService
```kotlin
class LocalVpnService : VpnService() {
    private var vpnInterface: ParcelFileDescriptor? = null
    private var isRunning = false
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if (!isRunning) {
            startVpn()
        }
        return START_STICKY
    }
    
    private fun startVpn() {
        val builder = Builder()
            .setSession("OfflineFirstVPN")
            .addAddress("********", 32)
            .addRoute("0.0.0.0", 0)
            .addDnsServer("*******")
            .setMtu(1500)
        
        vpnInterface = builder.establish()
        isRunning = true
        
        // بدء معالجة الحزم
        startPacketProcessing()
    }
    
    private fun startPacketProcessing() {
        Thread {
            val inputStream = FileInputStream(vpnInterface!!.fileDescriptor)
            val outputStream = FileOutputStream(vpnInterface!!.fileDescriptor)
            
            while (isRunning) {
                // قراءة ومعالجة الحزم
                processPackets(inputStream, outputStream)
            }
        }.start()
    }
}
```

### 3. نظام البروكسي والكاش (Proxy & Cache System)

#### Android Implementation
```kotlin
class CacheManager @Inject constructor(
    private val cacheDao: CacheDao,
    private val okHttpClient: OkHttpClient
) {
    private val cacheDir = File(context.cacheDir, "offline_cache")
    private val maxCacheSize = 100 * 1024 * 1024L // 100MB
    
    suspend fun fetchResource(url: String): Response {
        // البحث في الكاش أولاً
        val cachedResponse = cacheDao.getCachedResponse(url)
        if (cachedResponse != null && !isExpired(cachedResponse)) {
            return loadFromCache(cachedResponse)
        }
        
        // جلب من الإنترنت
        val response = okHttpClient.newCall(Request.Builder().url(url).build()).execute()
        
        // تخزين في الكاش
        if (response.isSuccessful) {
            saveToCache(url, response)
        }
        
        return response
    }
    
    private suspend fun saveToCache(url: String, response: Response) {
        val cacheEntry = CacheEntry(
            url = url,
            timestamp = System.currentTimeMillis(),
            size = response.body?.contentLength() ?: 0,
            contentType = response.header("Content-Type"),
            etag = response.header("ETag")
        )
        
        cacheDao.insertCacheEntry(cacheEntry)
        
        // حفظ محتوى الاستجابة
        val file = File(cacheDir, cacheEntry.id.toString())
        response.body?.byteStream()?.use { input ->
            file.outputStream().use { output ->
                input.copyTo(output)
            }
        }
    }
}
```

### 4. نظام Prefetch الذكي (Smart Prefetch System)

#### Android WorkManager Implementation
```kotlin
class PrefetchWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result {
        return try {
            val resourceList = getPrefetchResourceList()
            
            resourceList.forEach { resource ->
                if (!isStopped) {
                    prefetchResource(resource)
                    delay(1000) // تأخير بين الطلبات
                }
            }
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
    
    private suspend fun prefetchResource(resource: PrefetchResource) {
        val cacheManager = CacheManager.getInstance()
        cacheManager.fetchResource(resource.url)
    }
}

// جدولة Prefetch
class PrefetchScheduler @Inject constructor(
    private val workManager: WorkManager
) {
    fun schedulePrefetch() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.UNMETERED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val prefetchWork = PeriodicWorkRequestBuilder<PrefetchWorker>(6, TimeUnit.HOURS)
            .setConstraints(constraints)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            "prefetch_work",
            ExistingPeriodicWorkPolicy.KEEP,
            prefetchWork
        )
    }
}
```

## 📊 قاعدة البيانات

### Android Room Entities
```kotlin
@Entity(tableName = "cache_entries")
data class CacheEntry(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val url: String,
    val timestamp: Long,
    val size: Long,
    val contentType: String?,
    val etag: String?,
    val expiryTime: Long? = null
)

@Entity(tableName = "prefetch_resources")
data class PrefetchResource(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val url: String,
    val priority: Int = 0,
    val category: String,
    val isEnabled: Boolean = true
)

@Dao
interface CacheDao {
    @Query("SELECT * FROM cache_entries WHERE url = :url")
    suspend fun getCachedResponse(url: String): CacheEntry?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCacheEntry(entry: CacheEntry)
    
    @Query("DELETE FROM cache_entries WHERE timestamp < :expiredTime")
    suspend fun deleteExpiredEntries(expiredTime: Long)
    
    @Query("SELECT SUM(size) FROM cache_entries")
    suspend fun getTotalCacheSize(): Long
}
```

## 🔧 التكوين والإعدادات

### Android Manifest
```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- الصلاحيات المطلوبة -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.BIND_VPN_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    
    <application>
        <!-- خدمة VPN -->
        <service
            android:name=".vpn.LocalVpnService"
            android:permission="android.permission.BIND_VPN_SERVICE">
            <intent-filter>
                <action android:name="android.net.VpnService"/>
            </intent-filter>
        </service>
        
        <!-- Worker للـ Prefetch -->
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:foregroundServiceType="dataSync"/>
    </application>
</manifest>
```

### iOS Info.plist
```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>

<key>UIBackgroundModes</key>
<array>
    <string>background-processing</string>
    <string>background-fetch</string>
</array>

<key>NetworkExtensions</key>
<array>
    <string>com.apple.developer.networking.vpn.api</string>
</array>
```

## 🧪 استراتيجية الاختبار

### اختبارات الوحدة
```kotlin
@Test
fun `test cache hit returns cached response`() = runTest {
    // إعداد
    val url = "https://example.com/api/data"
    val cachedEntry = CacheEntry(url = url, timestamp = System.currentTimeMillis())
    cacheDao.insertCacheEntry(cachedEntry)
    
    // تنفيذ
    val response = cacheManager.fetchResource(url)
    
    // تحقق
    assertTrue(response.isFromCache)
}

@Test
fun `test network switch triggers VPN toggle`() = runTest {
    // إعداد
    val networkDetector = NetworkDetector(context)
    
    // محاكاة تغيير الشبكة
    networkDetector.simulateNetworkChange(NetworkType.HOME_WIFI)
    
    // تحقق
    verify(vpnManager).startVpnService()
}
```

## 📈 مقاييس الأداء

### مؤشرات الأداء الرئيسية
- **Cache Hit Ratio**: نسبة الطلبات المخدومة من الكاش
- **Data Savings**: كمية البيانات الموفرة (MB)
- **Prefetch Efficiency**: نسبة الموارد المجلبة مسبقاً والمستخدمة فعلياً
- **Battery Impact**: تأثير التطبيق على البطارية
- **Network Switch Time**: زمن التبديل بين أوضاع الشبكة

### تحسين الأداء
- استخدام Coroutines للعمليات غير المتزامنة
- تحسين حجم الكاش ديناميكياً
- ضغط البيانات المخزنة
- تنظيف الكاش المنتهي الصلاحية تلقائياً
