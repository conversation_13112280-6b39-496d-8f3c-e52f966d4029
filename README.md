# مدير الإنترنت أوفلاين أولاً
## Offline-First Internet Manager

تطبيق موبايل ذكي يدير استهلاك الإنترنت تلقائياً عبر التبديل بين VPN محلي للواي-فاي المنزلي ونظام كاش ذكي لبيانات الجوال.

## 🎯 الهدف

بناء تطبيق يقوم بـ:
- **VPN محلي** عند الاتصال بالواي-فاي المنزلي لاعتراض وتخزين كل المحتوى
- **Prefetch ذكي** لجلب المحتوى مسبقاً عند توفر شبكة غير محدودة
- **تبديل تلقائي** لوضع الكاش عند استخدام بيانات الجوال
- **توفير البيانات** عبر خدمة المحتوى المخزن مسبقاً

## 🏗️ المعمارية

### المكونات الأساسية

1. **طبقة كشف الشبكة**
   - مراقبة تغيير الشبكات (واي-فاي ↔ بيانات جوال)
   - كشف SSID المنزلي المحدد مسبقاً
   - تفعيل/إلغاء تفعيل VPN تلقائياً

2. **خدمة VPN المحلي**
   - اعتراض كل حركة الإنترنت الصادرة
   - إعادة توجيه عبر بروكسي داخلي
   - عنوان IP داخلي مخصص (********/32)

3. **نظام البروكسي والكاش**
   - بروكسي HTTP/HTTPS داخلي
   - كاش ذكي بحجم 50-100 ميغابايت
   - قاعدة بيانات للبيانات الوصفية

4. **نظام Prefetch**
   - جدولة مهام خلفية دورية
   - قائمة موارد قابلة للتخصيص
   - عمل فقط على شبكة غير محدودة

5. **واجهة المستخدم**
   - عرض حالة الاتصال الحالية
   - إحصائيات توفير البيانات
   - أزرار التحكم (مسح الكاش، Prefetch يدوي)

## 📱 المنصات المدعومة

### Android (Kotlin)
- **الحد الأدنى**: Android 7.0 (API 24)
- **التقنيات**: VpnService, WorkManager, OkHttp, Room, Dagger/Hilt
- **الصلاحيات**: VPN_SERVICE, INTERNET, ACCESS_NETWORK_STATE

### iOS (Swift)
- **الحد الأدنى**: iOS 13.0
- **التقنيات**: NetworkExtension, BackgroundTasks, URLSession, CoreData
- **الصلاحيات**: Network Extensions, Background App Refresh

## 🔧 التقنيات المستخدمة

### Android Stack
```
UI Layer: Jetpack Compose / XML
Business Logic: Kotlin + Coroutines
Network: OkHttp + Retrofit
Database: Room + SQLite
DI: Dagger/Hilt
Background: WorkManager
VPN: VpnService
```

### iOS Stack
```
UI Layer: SwiftUI / UIKit
Business Logic: Swift + Combine
Network: URLSession + Alamofire
Database: CoreData / Realm
Background: BGAppRefreshTask
VPN: NetworkExtension
```

## 📊 تدفق البيانات

1. **عند الاتصال بالواي-فاي المنزلي:**
   ```
   تطبيق → كاشف الشبكة → تشغيل VPN → بدء Prefetch
   ```

2. **عند طلب مورد (واي-فاي):**
   ```
   طلب → VPN → بروكسي → كاش (إذا موجود) أو إنترنت → تخزين → إرجاع
   ```

3. **عند التحول لبيانات الجوال:**
   ```
   كاشف الشبكة → إيقاف VPN → وضع الكاش فقط
   ```

4. **عند طلب مورد (بيانات جوال):**
   ```
   طلب → كاش (إذا موجود) أو بيانات جوال → تخزين → إرجاع
   ```

## 🚀 البدء السريع

### متطلبات التطوير

**Android:**
- Android Studio Arctic Fox+
- Kotlin 1.8+
- Gradle 7.0+

**iOS:**
- Xcode 14+
- Swift 5.7+
- iOS 13.0+ Deployment Target

### التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/your-repo/offline-first-internet-manager.git
cd offline-first-internet-manager

# Android
cd android
./gradlew build

# iOS
cd ios
xcodebuild -workspace OfflineFirstManager.xcworkspace -scheme OfflineFirstManager build
```

## 📋 خطة التطوير

- [x] تحليل المتطلبات والمعمارية
- [ ] إعداد مشروع Android
- [ ] تطوير VPN Service لـ Android
- [ ] تطوير نظام الكاش والبروكسي
- [ ] تطوير كاشف الشبكة والتبديل التلقائي
- [ ] تطوير نظام Prefetch
- [ ] تطوير واجهة المستخدم
- [ ] إعداد مشروع iOS
- [ ] تطوير Network Extension لـ iOS
- [ ] تطوير نظام الكاش لـ iOS
- [ ] تطوير Background Tasks لـ iOS
- [ ] كتابة الاختبارات الآلية
- [ ] إعداد CI/CD والتوثيق

## 🧪 الاختبار

### اختبارات الوحدة
- اختبار مكونات VPN
- اختبار نظام الكاش
- اختبار كاشف الشبكة
- اختبار Prefetch Manager

### اختبارات التكامل
- اختبار التبديل بين الشبكات
- اختبار Cache Hit/Miss
- اختبار Prefetch في الخلفية

## 📄 الترخيص

MIT License - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! راجع [CONTRIBUTING.md](CONTRIBUTING.md) للتفاصيل.
