package com.offlinefirst.internetmanager.network

import android.content.Context
import android.net.*
import android.net.wifi.WifiManager
import android.os.Build
import androidx.annotation.RequiresApi
import com.offlinefirst.internetmanager.data.database.dao.AppSettingsDao
import com.offlinefirst.internetmanager.data.database.dao.NetworkStatsDao
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * كاشف الشبكة لمراقبة تغييرات الاتصال
 */
@Singleton
class NetworkDetector @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appSettingsDao: AppSettingsDao,
    private val networkStatsDao: NetworkStatsDao
) {
    
    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    
    private val _networkType = MutableStateFlow(NetworkType.NONE)
    val networkType: StateFlow<NetworkType> = _networkType.asStateFlow()
    
    private val _isHomeWifi = MutableStateFlow(false)
    val isHomeWifi: StateFlow<Boolean> = _isHomeWifi.asStateFlow()
    
    private val _networkInfo = MutableStateFlow(NetworkInfo())
    val networkInfo: StateFlow<NetworkInfo> = _networkInfo.asStateFlow()
    
    private val detectorScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private var isMonitoring = false
    
    /**
     * بدء مراقبة الشبكة
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Timber.d("Network monitoring already started")
            return
        }
        
        try {
            // إنشاء callback للشبكة
            networkCallback = createNetworkCallback()
            
            // تسجيل callback
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                registerNetworkCallbackApi24()
            } else {
                registerNetworkCallbackLegacy()
            }
            
            // فحص الشبكة الحالية
            detectorScope.launch {
                checkCurrentNetwork()
            }
            
            isMonitoring = true
            Timber.i("Network monitoring started")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start network monitoring")
        }
    }
    
    /**
     * إيقاف مراقبة الشبكة
     */
    fun stopMonitoring() {
        if (!isMonitoring) {
            Timber.d("Network monitoring not started")
            return
        }
        
        try {
            networkCallback?.let { callback ->
                connectivityManager.unregisterNetworkCallback(callback)
            }
            
            networkCallback = null
            isMonitoring = false
            
            Timber.i("Network monitoring stopped")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to stop network monitoring")
        }
    }
    
    /**
     * إنشاء callback للشبكة
     */
    private fun createNetworkCallback(): ConnectivityManager.NetworkCallback {
        return object : ConnectivityManager.NetworkCallback() {
            
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Timber.d("Network available: $network")
                
                detectorScope.launch {
                    handleNetworkChange(network, true)
                }
            }
            
            override fun onLost(network: Network) {
                super.onLost(network)
                Timber.d("Network lost: $network")
                
                detectorScope.launch {
                    handleNetworkChange(network, false)
                }
            }
            
            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                super.onCapabilitiesChanged(network, networkCapabilities)
                Timber.d("Network capabilities changed: $network")
                
                detectorScope.launch {
                    handleNetworkChange(network, true)
                }
            }
            
            override fun onLinkPropertiesChanged(network: Network, linkProperties: LinkProperties) {
                super.onLinkPropertiesChanged(network, linkProperties)
                Timber.d("Network link properties changed: $network")
            }
        }
    }
    
    /**
     * تسجيل callback للشبكة (API 24+)
     */
    @RequiresApi(Build.VERSION_CODES.N)
    private fun registerNetworkCallbackApi24() {
        val request = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            .build()
        
        connectivityManager.registerDefaultNetworkCallback(networkCallback!!)
    }
    
    /**
     * تسجيل callback للشبكة (API أقل من 24)
     */
    private fun registerNetworkCallbackLegacy() {
        val request = NetworkRequest.Builder()
            .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
            .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(request, networkCallback!!)
    }
    
    /**
     * معالجة تغيير الشبكة
     */
    private suspend fun handleNetworkChange(network: Network, isAvailable: Boolean) {
        try {
            if (isAvailable) {
                val networkType = determineNetworkType(network)
                val isHome = isHomeWifiNetwork(network)
                
                updateNetworkState(networkType, isHome, network)
                
                // تحديث الإحصائيات
                networkStatsDao.incrementNetworkSwitch()
                
                Timber.i("Network changed to: $networkType (Home: $isHome)")
                
            } else {
                updateNetworkState(NetworkType.NONE, false, null)
                Timber.i("Network disconnected")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "Error handling network change")
        }
    }
    
    /**
     * تحديد نوع الشبكة
     */
    private fun determineNetworkType(network: Network): NetworkType {
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        
        return when {
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> {
                if (isHomeWifiNetwork(network)) {
                    NetworkType.HOME_WIFI
                } else {
                    NetworkType.OTHER_WIFI
                }
            }
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> {
                NetworkType.MOBILE_DATA
            }
            capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> {
                NetworkType.ETHERNET
            }
            else -> NetworkType.OTHER
        }
    }
    
    /**
     * التحقق من أن الشبكة واي-فاي منزلي
     */
    private suspend fun isHomeWifiNetwork(network: Network): Boolean {
        return try {
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            if (capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true) {
                val wifiInfo = wifiManager.connectionInfo
                val currentSSID = wifiInfo?.ssid?.removeSurrounding("\"")
                
                if (currentSSID != null) {
                    val settings = appSettingsDao.getSettings()
                    val homeSSIDs = settings?.homeWifiSSIDs ?: emptyList()
                    
                    homeSSIDs.contains(currentSSID)
                } else {
                    false
                }
            } else {
                false
            }
        } catch (e: Exception) {
            Timber.e(e, "Error checking home wifi")
            false
        }
    }
    
    /**
     * تحديث حالة الشبكة
     */
    private fun updateNetworkState(type: NetworkType, isHome: Boolean, network: Network?) {
        _networkType.value = type
        _isHomeWifi.value = isHome
        
        val info = if (network != null) {
            createNetworkInfo(network, type)
        } else {
            NetworkInfo()
        }
        
        _networkInfo.value = info
    }
    
    /**
     * إنشاء معلومات الشبكة
     */
    private fun createNetworkInfo(network: Network, type: NetworkType): NetworkInfo {
        return try {
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            val linkProperties = connectivityManager.getLinkProperties(network)
            
            NetworkInfo(
                networkType = type,
                isConnected = true,
                isMetered = capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_NOT_METERED) == false,
                signalStrength = getSignalStrength(capabilities),
                ssid = getCurrentSSID(),
                ipAddress = getIpAddress(linkProperties),
                dnsServers = getDnsServers(linkProperties)
            )
        } catch (e: Exception) {
            Timber.e(e, "Error creating network info")
            NetworkInfo(networkType = type, isConnected = true)
        }
    }
    
    /**
     * فحص الشبكة الحالية
     */
    private suspend fun checkCurrentNetwork() {
        try {
            val activeNetwork = connectivityManager.activeNetwork
            
            if (activeNetwork != null) {
                handleNetworkChange(activeNetwork, true)
            } else {
                updateNetworkState(NetworkType.NONE, false, null)
            }
        } catch (e: Exception) {
            Timber.e(e, "Error checking current network")
        }
    }
    
    /**
     * الحصول على قوة الإشارة
     */
    private fun getSignalStrength(capabilities: NetworkCapabilities?): Int {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                capabilities?.signalStrength ?: 0
            } else {
                // للإصدارات الأقدم، استخدم WifiManager
                val wifiInfo = wifiManager.connectionInfo
                wifiInfo?.rssi ?: 0
            }
        } catch (e: Exception) {
            Timber.w(e, "Failed to get signal strength")
            0
        }
    }
    
    /**
     * الحصول على SSID الحالي
     */
    private fun getCurrentSSID(): String? {
        return try {
            val wifiInfo = wifiManager.connectionInfo
            wifiInfo?.ssid?.removeSurrounding("\"")
        } catch (e: Exception) {
            Timber.w(e, "Failed to get current SSID")
            null
        }
    }
    
    /**
     * الحصول على عنوان IP
     */
    private fun getIpAddress(linkProperties: LinkProperties?): String? {
        return try {
            linkProperties?.linkAddresses?.firstOrNull()?.address?.hostAddress
        } catch (e: Exception) {
            Timber.w(e, "Failed to get IP address")
            null
        }
    }
    
    /**
     * الحصول على خوادم DNS
     */
    private fun getDnsServers(linkProperties: LinkProperties?): List<String> {
        return try {
            linkProperties?.dnsServers?.map { it.hostAddress } ?: emptyList()
        } catch (e: Exception) {
            Timber.w(e, "Failed to get DNS servers")
            emptyList()
        }
    }
    
    /**
     * الحصول على نوع الشبكة الحالي
     */
    fun getCurrentNetworkType(): NetworkType = _networkType.value
    
    /**
     * التحقق من الاتصال بواي-فاي منزلي
     */
    fun isCurrentlyOnHomeWifi(): Boolean = _isHomeWifi.value
    
    /**
     * التحقق من وجود اتصال بالإنترنت
     */
    fun hasInternetConnection(): Boolean {
        return _networkType.value != NetworkType.NONE
    }
    
    /**
     * التحقق من أن الشبكة محدودة (مدفوعة)
     */
    fun isCurrentNetworkMetered(): Boolean {
        return _networkInfo.value.isMetered
    }
    
    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        stopMonitoring()
        detectorScope.cancel()
    }
}

/**
 * أنواع الشبكات
 */
enum class NetworkType {
    NONE,
    HOME_WIFI,
    OTHER_WIFI,
    MOBILE_DATA,
    ETHERNET,
    OTHER
}

/**
 * معلومات الشبكة
 */
data class NetworkInfo(
    val networkType: NetworkType = NetworkType.NONE,
    val isConnected: Boolean = false,
    val isMetered: Boolean = false,
    val signalStrength: Int = 0,
    val ssid: String? = null,
    val ipAddress: String? = null,
    val dnsServers: List<String> = emptyList(),
    val timestamp: Long = System.currentTimeMillis()
)
