package com.offlinefirst.internetmanager.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.offlinefirst.internetmanager.network.NetworkMonitorServiceHelper
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * مستقبل إعادة تشغيل الجهاز
 */
@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            when (intent.action) {
                Intent.ACTION_BOOT_COMPLETED -> {
                    handleBootCompleted(context)
                }
                Intent.ACTION_MY_PACKAGE_REPLACED,
                Intent.ACTION_PACKAGE_REPLACED -> {
                    if (intent.dataString?.contains(context.packageName) == true) {
                        handlePackageReplaced(context)
                    }
                }
                else -> {
                    Timber.d("Received unknown boot action: ${intent.action}")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error handling boot event: ${intent.action}")
        }
    }
    
    /**
     * معالجة اكتمال إعادة التشغيل
     */
    private fun handleBootCompleted(context: Context) {
        Timber.i("Device boot completed, starting network monitoring")
        
        try {
            // بدء خدمة مراقبة الشبكة
            NetworkMonitorServiceHelper.startService(context)
            
            Timber.i("Network monitoring service started after boot")
        } catch (e: Exception) {
            Timber.e(e, "Failed to start services after boot")
        }
    }
    
    /**
     * معالجة استبدال التطبيق
     */
    private fun handlePackageReplaced(context: Context) {
        Timber.i("Package replaced, restarting network monitoring")
        
        try {
            // إعادة بدء خدمة مراقبة الشبكة
            NetworkMonitorServiceHelper.startService(context)
            
            Timber.i("Network monitoring service restarted after package replacement")
        } catch (e: Exception) {
            Timber.e(e, "Failed to restart services after package replacement")
        }
    }
}
