package com.offlinefirst.internetmanager.network

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.offlinefirst.internetmanager.OfflineFirstApplication
import com.offlinefirst.internetmanager.R
import com.offlinefirst.internetmanager.ui.MainActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject

/**
 * خدمة مراقبة الشبكة في الخلفية
 */
@AndroidEntryPoint
class NetworkMonitorService : Service() {
    
    @Inject
    lateinit var networkDetector: NetworkDetector
    
    @Inject
    lateinit var autoSwitchManager: AutoSwitchManager
    
    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var isMonitoring = false
    
    companion object {
        const val ACTION_START_MONITORING = "com.offlinefirst.internetmanager.START_MONITORING"
        const val ACTION_STOP_MONITORING = "com.offlinefirst.internetmanager.STOP_MONITORING"
        
        private const val NOTIFICATION_UPDATE_INTERVAL = 5000L // 5 ثوانٍ
    }
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("NetworkMonitorService created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("NetworkMonitorService onStartCommand: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_MONITORING -> startMonitoring()
            ACTION_STOP_MONITORING -> stopMonitoring()
            else -> startMonitoring() // افتراضي
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Timber.d("NetworkMonitorService destroyed")
        stopMonitoring()
        serviceScope.cancel()
    }
    
    /**
     * بدء مراقبة الشبكة
     */
    private fun startMonitoring() {
        if (isMonitoring) {
            Timber.d("Network monitoring already started")
            return
        }
        
        try {
            // بدء الإشعار المقدم
            startForeground(
                OfflineFirstApplication.NOTIFICATION_NETWORK_MONITOR,
                createMonitoringNotification()
            )
            
            // بدء مراقبة الشبكة
            networkDetector.startMonitoring()
            
            // بدء التبديل التلقائي
            autoSwitchManager.startAutoSwitch()
            
            // بدء تحديث الإشعارات
            startNotificationUpdates()
            
            isMonitoring = true
            Timber.i("Network monitoring service started")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to start network monitoring service")
            stopSelf()
        }
    }
    
    /**
     * إيقاف مراقبة الشبكة
     */
    private fun stopMonitoring() {
        if (!isMonitoring) {
            Timber.d("Network monitoring not started")
            return
        }
        
        try {
            // إيقاف التبديل التلقائي
            autoSwitchManager.stopAutoSwitch()
            
            // إيقاف مراقبة الشبكة
            networkDetector.stopMonitoring()
            
            // إيقاف الإشعار المقدم
            stopForeground(STOP_FOREGROUND_REMOVE)
            
            isMonitoring = false
            Timber.i("Network monitoring service stopped")
            
        } catch (e: Exception) {
            Timber.e(e, "Error stopping network monitoring service")
        }
    }
    
    /**
     * إنشاء إشعار المراقبة
     */
    private fun createMonitoringNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, OfflineFirstApplication.CHANNEL_NETWORK_MONITOR)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(getString(R.string.notification_network_monitoring))
            .setSmallIcon(R.drawable.ic_network_monitor)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    /**
     * بدء تحديث الإشعارات
     */
    private fun startNotificationUpdates() {
        serviceScope.launch {
            while (isMonitoring) {
                try {
                    updateNotification()
                    delay(NOTIFICATION_UPDATE_INTERVAL)
                } catch (e: Exception) {
                    Timber.e(e, "Error updating notification")
                    delay(NOTIFICATION_UPDATE_INTERVAL * 2) // انتظار أطول في حالة الخطأ
                }
            }
        }
    }
    
    /**
     * تحديث الإشعار
     */
    private fun updateNotification() {
        try {
            val networkType = networkDetector.getCurrentNetworkType()
            val isHomeWifi = networkDetector.isCurrentlyOnHomeWifi()
            
            val statusText = when (networkType) {
                NetworkType.HOME_WIFI -> getString(R.string.network_home_wifi)
                NetworkType.OTHER_WIFI -> getString(R.string.network_other_wifi)
                NetworkType.MOBILE_DATA -> getString(R.string.network_mobile_data)
                NetworkType.NONE -> getString(R.string.network_none)
                else -> getString(R.string.network_other)
            }
            
            val notification = createUpdatedNotification(statusText, isHomeWifi)
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.notify(
                OfflineFirstApplication.NOTIFICATION_NETWORK_MONITOR,
                notification
            )
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to update notification")
        }
    }
    
    /**
     * إنشاء إشعار محدث
     */
    private fun createUpdatedNotification(statusText: String, isHomeWifi: Boolean): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val contentText = if (isHomeWifi) {
            "$statusText - ${getString(R.string.vpn_mode_active)}"
        } else {
            "$statusText - ${getString(R.string.cache_mode_active)}"
        }
        
        return NotificationCompat.Builder(this, OfflineFirstApplication.CHANNEL_NETWORK_MONITOR)
            .setContentTitle(getString(R.string.app_name))
            .setContentText(contentText)
            .setSmallIcon(getNetworkIcon(isHomeWifi))
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    /**
     * الحصول على أيقونة الشبكة
     */
    private fun getNetworkIcon(isHomeWifi: Boolean): Int {
        return if (isHomeWifi) {
            R.drawable.ic_vpn
        } else {
            R.drawable.ic_cache
        }
    }
}

/**
 * مساعد لبدء وإيقاف خدمة مراقبة الشبكة
 */
object NetworkMonitorServiceHelper {
    
    /**
     * بدء خدمة مراقبة الشبكة
     */
    fun startService(context: android.content.Context) {
        val intent = Intent(context, NetworkMonitorService::class.java).apply {
            action = NetworkMonitorService.ACTION_START_MONITORING
        }
        context.startForegroundService(intent)
    }
    
    /**
     * إيقاف خدمة مراقبة الشبكة
     */
    fun stopService(context: android.content.Context) {
        val intent = Intent(context, NetworkMonitorService::class.java).apply {
            action = NetworkMonitorService.ACTION_STOP_MONITORING
        }
        context.stopService(intent)
    }
}
