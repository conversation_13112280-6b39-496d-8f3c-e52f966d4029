package com.offlinefirst.internetmanager.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.UUID

/**
 * نموذج بيانات إدخال الكاش
 */
@Entity(tableName = "cache_entries")
data class CacheEntry(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    
    /** رابط المورد */
    val url: String,
    
    /** الطابع الزمني لحفظ المورد */
    val timestamp: Long = System.currentTimeMillis(),
    
    /** حجم المورد بالبايت */
    val size: Long,
    
    /** نوع المحتوى */
    val contentType: String? = null,
    
    /** ETag للتحقق من التحديثات */
    val etag: String? = null,
    
    /** Last-Modified header */
    val lastModified: String? = null,
    
    /** وقت انتهاء الصلاحية */
    val expiryTime: Long? = null,
    
    /** عدد مرات الوصول */
    val accessCount: Int = 0,
    
    /** آخر وقت وصول */
    val lastAccessTime: Long = System.currentTimeMillis(),
    
    /** مسار الملف المحلي */
    val filePath: String? = null,
    
    /** هل المورد مضغوط */
    val isCompressed: Boolean = false,
    
    /** رمز الاستجابة HTTP */
    val responseCode: Int = 200,
    
    /** رؤوس الاستجابة المهمة */
    val headers: String? = null
) {
    
    /**
     * التحقق من انتهاء صلاحية المورد
     */
    fun isExpired(): Boolean {
        return expiryTime?.let { it < System.currentTimeMillis() } ?: false
    }
    
    /**
     * التحقق من قدم المورد (أكثر من 24 ساعة)
     */
    fun isStale(): Boolean {
        val dayInMillis = 24 * 60 * 60 * 1000
        return System.currentTimeMillis() - timestamp > dayInMillis
    }
    
    /**
     * حساب عمر المورد بالساعات
     */
    fun getAgeInHours(): Long {
        return (System.currentTimeMillis() - timestamp) / (60 * 60 * 1000)
    }
    
    /**
     * تحديث إحصائيات الوصول
     */
    fun updateAccess(): CacheEntry {
        return copy(
            accessCount = accessCount + 1,
            lastAccessTime = System.currentTimeMillis()
        )
    }
}

/**
 * نموذج بيانات مورد Prefetch
 */
@Entity(tableName = "prefetch_resources")
data class PrefetchResource(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    
    /** رابط المورد */
    val url: String,
    
    /** أولوية الجلب (أعلى رقم = أولوية أعلى) */
    val priority: Int = 0,
    
    /** فئة المورد (صور، فيديو، صفحات ويب، إلخ) */
    val category: String,
    
    /** هل المورد مفعل للـ Prefetch */
    val isEnabled: Boolean = true,
    
    /** آخر مرة تم جلب المورد */
    val lastFetched: Long? = null,
    
    /** عدد مرات الجلب */
    val fetchCount: Int = 0,
    
    /** عدد مرات فشل الجلب */
    val failureCount: Int = 0,
    
    /** آخر خطأ حدث */
    val lastError: String? = null,
    
    /** تكرار الجلب بالساعات */
    val fetchIntervalHours: Int = 6,
    
    /** الحد الأقصى لحجم المورد بالبايت */
    val maxSizeBytes: Long? = null,
    
    /** تاريخ الإنشاء */
    val createdAt: Long = System.currentTimeMillis(),
    
    /** تاريخ آخر تحديث */
    val updatedAt: Long = System.currentTimeMillis()
) {
    
    /**
     * التحقق من الحاجة لإعادة الجلب
     */
    fun needsRefetch(): Boolean {
        if (!isEnabled) return false
        
        val lastFetchTime = lastFetched ?: 0
        val intervalMillis = fetchIntervalHours * 60 * 60 * 1000
        
        return System.currentTimeMillis() - lastFetchTime > intervalMillis
    }
    
    /**
     * تحديث إحصائيات الجلب الناجح
     */
    fun updateSuccessfulFetch(): PrefetchResource {
        return copy(
            lastFetched = System.currentTimeMillis(),
            fetchCount = fetchCount + 1,
            lastError = null,
            updatedAt = System.currentTimeMillis()
        )
    }
    
    /**
     * تحديث إحصائيات الجلب الفاشل
     */
    fun updateFailedFetch(error: String): PrefetchResource {
        return copy(
            failureCount = failureCount + 1,
            lastError = error,
            updatedAt = System.currentTimeMillis()
        )
    }
}

/**
 * نموذج بيانات إحصائيات الشبكة
 */
@Entity(tableName = "network_stats")
data class NetworkStats(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    
    /** تاريخ الإحصائية */
    val date: String, // YYYY-MM-DD
    
    /** إجمالي البيانات المستهلكة بالبايت */
    val totalDataUsed: Long = 0,
    
    /** البيانات الموفرة من الكاش بالبايت */
    val dataSavedFromCache: Long = 0,
    
    /** عدد طلبات Cache Hit */
    val cacheHitCount: Int = 0,
    
    /** عدد طلبات Cache Miss */
    val cacheMissCount: Int = 0,
    
    /** وقت تشغيل VPN بالدقائق */
    val vpnActiveMinutes: Int = 0,
    
    /** عدد مرات تبديل الشبكة */
    val networkSwitchCount: Int = 0,
    
    /** عدد موارد Prefetch المجلبة */
    val prefetchedResourcesCount: Int = 0,
    
    /** حجم بيانات Prefetch بالبايت */
    val prefetchedDataSize: Long = 0
) {
    
    /**
     * حساب نسبة Cache Hit
     */
    fun getCacheHitRatio(): Float {
        val totalRequests = cacheHitCount + cacheMissCount
        return if (totalRequests > 0) {
            cacheHitCount.toFloat() / totalRequests * 100
        } else 0f
    }
    
    /**
     * حساب نسبة توفير البيانات
     */
    fun getDataSavingRatio(): Float {
        val totalPotentialData = totalDataUsed + dataSavedFromCache
        return if (totalPotentialData > 0) {
            dataSavedFromCache.toFloat() / totalPotentialData * 100
        } else 0f
    }
}

/**
 * نموذج بيانات إعدادات التطبيق
 */
@Entity(tableName = "app_settings")
data class AppSettings(
    @PrimaryKey
    val id: String = "default",
    
    /** قائمة SSID الشبكات المنزلية */
    val homeWifiSSIDs: List<String> = emptyList(),
    
    /** الحد الأقصى لحجم الكاش بالبايت */
    val maxCacheSize: Long = 100 * 1024 * 1024, // 100MB
    
    /** فترة Prefetch بالساعات */
    val prefetchIntervalHours: Int = 6,
    
    /** مدة انتهاء صلاحية الكاش بالساعات */
    val cacheExpiryHours: Int = 24,
    
    /** تفعيل Prefetch */
    val enablePrefetch: Boolean = true,
    
    /** تفعيل VPN */
    val enableVpn: Boolean = true,
    
    /** Prefetch فقط على الواي-فاي */
    val prefetchOnlyOnWifi: Boolean = true,
    
    /** Prefetch فقط عند الشحن */
    val prefetchOnlyWhenCharging: Boolean = false,
    
    /** تفعيل ضغط البيانات */
    val enableCompression: Boolean = true,
    
    /** تفعيل الإشعارات */
    val enableNotifications: Boolean = true,
    
    /** تفعيل السجلات المفصلة */
    val enableVerboseLogging: Boolean = false
)
