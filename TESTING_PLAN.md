# خطة الاختبار الشاملة
## Comprehensive Testing Plan

## 🧪 استراتيجية الاختبار

### 1. اختبارات الوحدة (Unit Tests)

#### اختبار مدير الكاش (Cache Manager Tests)
```kotlin
@RunWith(MockitoJUnitRunner::class)
class CacheManagerTest {
    
    @Mock private lateinit var cacheDao: CacheDao
    @Mock private lateinit var context: Context
    
    private lateinit var cacheManager: CacheManager
    
    @Before
    fun setup() {
        cacheManager = CacheManager(cacheDao, context)
    }
    
    @Test
    fun `fetchFromCache returns cached data when available`() = runTest {
        // إعداد
        val url = "https://example.com/api/data"
        val cachedEntry = CacheEntry(
            url = url,
            timestamp = System.currentTimeMillis(),
            size = 1024
        )
        `when`(cacheDao.getCachedResponse(url)).thenReturn(cachedEntry)
        
        // تنفيذ
        val result = cacheManager.fetchFromCache(url)
        
        // تحقق
        assertTrue(result is CacheResult.Hit)
        verify(cacheDao).getCachedResponse(url)
    }
    
    @Test
    fun `fetchFromCache returns miss when data expired`() = runTest {
        // إعداد
        val url = "https://example.com/api/data"
        val expiredEntry = CacheEntry(
            url = url,
            timestamp = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(2),
            size = 1024
        )
        `when`(cacheDao.getCachedResponse(url)).thenReturn(expiredEntry)
        
        // تنفيذ
        val result = cacheManager.fetchFromCache(url)
        
        // تحقق
        assertTrue(result is CacheResult.Miss)
    }
    
    @Test
    fun `clearCache removes all entries and files`() = runTest {
        // إعداد
        val totalSize = 5000L
        `when`(cacheDao.getTotalCacheSize()).thenReturn(totalSize)
        
        // تنفيذ
        val result = cacheManager.clearCache()
        
        // تحقق
        assertTrue(result.isSuccess)
        assertEquals(totalSize, result.getOrNull())
        verify(cacheDao).deleteAllEntries()
    }
}
```

#### اختبار كاشف الشبكة (Network Detector Tests)
```kotlin
@RunWith(RobolectricTestRunner::class)
class NetworkDetectorTest {
    
    @Mock private lateinit var connectivityManager: ConnectivityManager
    @Mock private lateinit var vpnManager: VpnManager
    
    private lateinit var networkDetector: NetworkDetector
    
    @Before
    fun setup() {
        networkDetector = NetworkDetector(
            connectivityManager = connectivityManager,
            vpnManager = vpnManager
        )
    }
    
    @Test
    fun `network change to home wifi starts VPN`() {
        // إعداد
        val homeWifiNetwork = mockNetwork(NetworkType.HOME_WIFI)
        
        // تنفيذ
        networkDetector.onNetworkAvailable(homeWifiNetwork)
        
        // تحقق
        verify(vpnManager).startVpnService()
    }
    
    @Test
    fun `network change to mobile data stops VPN`() {
        // إعداد
        val mobileNetwork = mockNetwork(NetworkType.MOBILE_DATA)
        
        // تنفيذ
        networkDetector.onNetworkAvailable(mobileNetwork)
        
        // تحقق
        verify(vpnManager).stopVpnService()
    }
    
    @Test
    fun `network lost stops VPN`() {
        // إعداد
        val network = mockNetwork(NetworkType.HOME_WIFI)
        
        // تنفيذ
        networkDetector.onNetworkLost(network)
        
        // تحقق
        verify(vpnManager).stopVpnService()
    }
}
```

#### اختبار Prefetch Manager
```kotlin
@RunWith(MockitoJUnitRunner::class)
class PrefetchManagerTest {
    
    @Mock private lateinit var workManager: WorkManager
    @Mock private lateinit var resourceDao: PrefetchResourceDao
    
    private lateinit var prefetchManager: PrefetchManager
    
    @Before
    fun setup() {
        prefetchManager = PrefetchManager(workManager, resourceDao)
    }
    
    @Test
    fun `enqueuePrefetch schedules work with correct constraints`() {
        // تنفيذ
        val workId = prefetchManager.enqueuePrefetch()
        
        // تحقق
        assertNotNull(workId)
        verify(workManager).enqueue(any<WorkRequest>())
    }
    
    @Test
    fun `cancelPrefetch cancels scheduled work`() {
        // إعداد
        val workId = UUID.randomUUID()
        
        // تنفيذ
        prefetchManager.cancelPrefetch(workId)
        
        // تحقق
        verify(workManager).cancelWorkById(workId)
    }
}
```

### 2. اختبارات التكامل (Integration Tests)

#### اختبار تدفق البيانات الكامل
```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class DataFlowIntegrationTest {
    
    @get:Rule
    val activityRule = ActivityTestRule(MainActivity::class.java)
    
    private lateinit var cacheManager: CacheManager
    private lateinit var networkDetector: NetworkDetector
    private lateinit var vpnService: LocalVpnService
    
    @Before
    fun setup() {
        // إعداد المكونات الحقيقية
        cacheManager = CacheManager.getInstance()
        networkDetector = NetworkDetector.getInstance()
        vpnService = LocalVpnService()
    }
    
    @Test
    fun `complete flow from network detection to cache storage`() = runTest {
        // 1. محاكاة الاتصال بواي-فاي منزلي
        networkDetector.simulateNetworkChange(NetworkType.HOME_WIFI)
        
        // 2. التحقق من تشغيل VPN
        delay(1000)
        assertTrue(vpnService.isRunning())
        
        // 3. محاكاة طلب HTTP
        val testUrl = "https://httpbin.org/json"
        val response = cacheManager.fetchResource(testUrl)
        
        // 4. التحقق من تخزين البيانات في الكاش
        assertTrue(response.isSuccessful)
        val cachedResponse = cacheManager.fetchFromCache(testUrl)
        assertTrue(cachedResponse is CacheResult.Hit)
        
        // 5. محاكاة التحول لبيانات الجوال
        networkDetector.simulateNetworkChange(NetworkType.MOBILE_DATA)
        
        // 6. التحقق من إيقاف VPN
        delay(1000)
        assertFalse(vpnService.isRunning())
        
        // 7. التحقق من خدمة البيانات من الكاش
        val cachedResponse2 = cacheManager.fetchFromCache(testUrl)
        assertTrue(cachedResponse2 is CacheResult.Hit)
    }
}
```

#### اختبار Prefetch في الخلفية
```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class PrefetchIntegrationTest {
    
    @Test
    fun `prefetch worker fetches and caches resources`() = runTest {
        // إعداد قائمة موارد للـ Prefetch
        val resources = listOf(
            PrefetchResource("https://httpbin.org/json", priority = 1),
            PrefetchResource("https://httpbin.org/xml", priority = 2)
        )
        
        // تشغيل Prefetch Worker
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        val worker = TestListenableWorkerBuilder<PrefetchWorker>(context).build()
        
        val result = worker.doWork()
        
        // التحقق من نجاح العملية
        assertEquals(ListenableWorker.Result.success(), result)
        
        // التحقق من تخزين الموارد في الكاش
        val cacheManager = CacheManager.getInstance()
        resources.forEach { resource ->
            val cachedResult = cacheManager.fetchFromCache(resource.url)
            assertTrue(cachedResult is CacheResult.Hit)
        }
    }
}
```

### 3. اختبارات واجهة المستخدم (UI Tests)

#### اختبار الشاشة الرئيسية
```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityUITest {
    
    @get:Rule
    val activityRule = ActivityTestRule(MainActivity::class.java)
    
    @Test
    fun `main screen displays network status correctly`() {
        // التحقق من عرض حالة الشبكة
        onView(withId(R.id.network_status_text))
            .check(matches(isDisplayed()))
        
        // التحقق من عرض إحصائيات توفير البيانات
        onView(withId(R.id.data_savings_text))
            .check(matches(isDisplayed()))
        
        // التحقق من وجود أزرار التحكم
        onView(withId(R.id.clear_cache_button))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.manual_prefetch_button))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun `clear cache button clears cache and updates UI`() {
        // النقر على زر مسح الكاش
        onView(withId(R.id.clear_cache_button))
            .perform(click())
        
        // التحقق من ظهور رسالة التأكيد
        onView(withText("تم مسح الكاش بنجاح"))
            .check(matches(isDisplayed()))
        
        // التحقق من تحديث إحصائيات الكاش
        onView(withId(R.id.cache_size_text))
            .check(matches(withText("0 MB")))
    }
    
    @Test
    fun `manual prefetch button triggers prefetch`() {
        // النقر على زر Prefetch اليدوي
        onView(withId(R.id.manual_prefetch_button))
            .perform(click())
        
        // التحقق من ظهور مؤشر التحميل
        onView(withId(R.id.prefetch_progress))
            .check(matches(isDisplayed()))
        
        // انتظار انتهاء العملية
        Thread.sleep(5000)
        
        // التحقق من اختفاء مؤشر التحميل
        onView(withId(R.id.prefetch_progress))
            .check(matches(not(isDisplayed())))
    }
}
```

### 4. اختبارات الأداء (Performance Tests)

#### اختبار أداء الكاش
```kotlin
@RunWith(AndroidJUnit4::class)
class CachePerformanceTest {
    
    @Test
    fun `cache lookup performance under load`() = runTest {
        val cacheManager = CacheManager.getInstance()
        val urls = (1..1000).map { "https://example.com/api/data$it" }
        
        // ملء الكاش
        urls.forEach { url ->
            cacheManager.saveToCache(url, "test data".toByteArray())
        }
        
        // قياس أداء البحث
        val startTime = System.currentTimeMillis()
        
        urls.forEach { url ->
            cacheManager.fetchFromCache(url)
        }
        
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        // التحقق من أن الأداء مقبول (أقل من ثانية واحدة)
        assertTrue("Cache lookup took too long: ${duration}ms", duration < 1000)
    }
    
    @Test
    fun `memory usage during cache operations`() {
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        val cacheManager = CacheManager.getInstance()
        
        // إجراء عمليات كاش مكثفة
        repeat(100) {
            val data = ByteArray(1024 * 1024) // 1MB
            cacheManager.saveToCache("test_url_$it", data)
        }
        
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()
        val memoryIncrease = finalMemory - initialMemory
        
        // التحقق من أن استهلاك الذاكرة معقول (أقل من 50MB)
        assertTrue("Memory usage too high: ${memoryIncrease / 1024 / 1024}MB", 
                  memoryIncrease < 50 * 1024 * 1024)
    }
}
```

### 5. اختبارات الأمان (Security Tests)

#### اختبار أمان VPN
```kotlin
@RunWith(AndroidJUnit4::class)
class VpnSecurityTest {
    
    @Test
    fun `VPN service requires proper permissions`() {
        val context = InstrumentationRegistry.getInstrumentation().targetContext
        
        // التحقق من وجود صلاحية VPN
        val permission = context.checkSelfPermission(android.Manifest.permission.BIND_VPN_SERVICE)
        assertEquals(PackageManager.PERMISSION_GRANTED, permission)
    }
    
    @Test
    fun `VPN tunnel encrypts traffic properly`() {
        // اختبار تشفير البيانات في النفق
        val vpnService = LocalVpnService()
        val testData = "sensitive data".toByteArray()
        
        val encryptedData = vpnService.encryptData(testData)
        assertNotEquals(testData.contentToString(), encryptedData.contentToString())
        
        val decryptedData = vpnService.decryptData(encryptedData)
        assertEquals(testData.contentToString(), decryptedData.contentToString())
    }
}
```

## 📊 تقارير الاختبار

### مقاييس التغطية المطلوبة
- **تغطية الكود**: 85% كحد أدنى
- **تغطية الفروع**: 80% كحد أدنى
- **تغطية الدوال**: 90% كحد أدنى

### أدوات القياس
```gradle
// Android
android {
    buildTypes {
        debug {
            testCoverageEnabled true
        }
    }
}

dependencies {
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.robolectric:robolectric:4.8.1'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}
```

### تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
./gradlew test

# تشغيل اختبارات الوحدة فقط
./gradlew testDebugUnitTest

# تشغيل اختبارات التكامل
./gradlew connectedAndroidTest

# إنتاج تقرير التغطية
./gradlew jacocoTestReport
```

## 🔄 التكامل المستمر (CI/CD Testing)

### GitHub Actions Workflow
```yaml
name: Android CI

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 11
      uses: actions/setup-java@v3
      with:
        java-version: '11'
        distribution: 'temurin'
        
    - name: Run unit tests
      run: ./gradlew testDebugUnitTest
      
    - name: Generate test report
      run: ./gradlew jacocoTestReport
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
```
