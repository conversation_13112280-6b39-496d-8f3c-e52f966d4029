package com.offlinefirst.internetmanager.core

import com.offlinefirst.internetmanager.cache.CacheManager
import com.offlinefirst.internetmanager.proxy.ProxyManager
import com.offlinefirst.internetmanager.proxy.ProxyResult
import com.offlinefirst.internetmanager.vpn.VpnManager
import com.offlinefirst.internetmanager.vpn.VpnResult
import com.offlinefirst.internetmanager.vpn.VpnState
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير الشبكة الرئيسي للتكامل بين VPN والبروكسي والكاش
 */
@Singleton
class NetworkManager @Inject constructor(
    private val vpnManager: VpnManager,
    private val proxyManager: ProxyManager,
    private val cacheManager: CacheManager
) {
    
    private val _networkState = MutableStateFlow(NetworkState.DISCONNECTED)
    val networkState: StateFlow<NetworkState> = _networkState.asStateFlow()
    
    private val _isVpnMode = MutableStateFlow(false)
    val isVpnMode: StateFlow<Boolean> = _isVpnMode.asStateFlow()
    
    private val managerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    init {
        // مراقبة حالة VPN
        managerScope.launch {
            vpnManager.vpnState.collect { vpnState ->
                updateNetworkState(vpnState)
            }
        }
    }
    
    /**
     * تفعيل وضع VPN (للواي-فاي المنزلي)
     */
    suspend fun enableVpnMode(): NetworkResult {
        return withContext(Dispatchers.Main) {
            try {
                Timber.d("Enabling VPN mode")
                _networkState.value = NetworkState.CONNECTING
                
                // بدء البروكسي أولاً
                when (val proxyResult = proxyManager.startProxy()) {
                    is ProxyResult.Success -> {
                        Timber.d("Proxy started successfully")
                    }
                    is ProxyResult.Error -> {
                        Timber.e("Failed to start proxy: ${proxyResult.message}")
                        _networkState.value = NetworkState.ERROR
                        return@withContext NetworkResult.Error("Failed to start proxy: ${proxyResult.message}")
                    }
                }
                
                // بدء VPN
                when (val vpnResult = vpnManager.startVpnService()) {
                    is VpnResult.Success -> {
                        _isVpnMode.value = true
                        _networkState.value = NetworkState.VPN_CONNECTED
                        Timber.i("VPN mode enabled successfully")
                        NetworkResult.Success
                    }
                    is VpnResult.PermissionRequired -> {
                        _networkState.value = NetworkState.PERMISSION_REQUIRED
                        NetworkResult.PermissionRequired(vpnResult.intent)
                    }
                    is VpnResult.Error -> {
                        // إيقاف البروكسي في حالة فشل VPN
                        proxyManager.stopProxy()
                        _networkState.value = NetworkState.ERROR
                        NetworkResult.Error("Failed to start VPN: ${vpnResult.message}")
                    }
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to enable VPN mode")
                _networkState.value = NetworkState.ERROR
                NetworkResult.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * إلغاء تفعيل وضع VPN (للتحول لبيانات الجوال)
     */
    suspend fun disableVpnMode(): NetworkResult {
        return withContext(Dispatchers.Main) {
            try {
                Timber.d("Disabling VPN mode")
                _networkState.value = NetworkState.DISCONNECTING
                
                // إيقاف VPN
                when (val vpnResult = vpnManager.stopVpnService()) {
                    is VpnResult.Success -> {
                        Timber.d("VPN stopped successfully")
                    }
                    is VpnResult.Error -> {
                        Timber.w("Error stopping VPN: ${vpnResult.message}")
                    }
                    else -> {
                        Timber.w("Unexpected VPN result: $vpnResult")
                    }
                }
                
                // إيقاف البروكسي
                when (val proxyResult = proxyManager.stopProxy()) {
                    is ProxyResult.Success -> {
                        Timber.d("Proxy stopped successfully")
                    }
                    is ProxyResult.Error -> {
                        Timber.w("Error stopping proxy: ${proxyResult.message}")
                    }
                }
                
                _isVpnMode.value = false
                _networkState.value = NetworkState.CACHE_ONLY
                
                Timber.i("VPN mode disabled, switched to cache-only mode")
                NetworkResult.Success
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to disable VPN mode")
                _networkState.value = NetworkState.ERROR
                NetworkResult.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * التحول لوضع الكاش فقط (بيانات الجوال)
     */
    suspend fun switchToCacheOnlyMode(): NetworkResult {
        return if (_isVpnMode.value) {
            disableVpnMode()
        } else {
            _networkState.value = NetworkState.CACHE_ONLY
            Timber.d("Already in cache-only mode")
            NetworkResult.Success
        }
    }
    
    /**
     * التحول لوضع VPN (واي-فاي منزلي)
     */
    suspend fun switchToVpnMode(): NetworkResult {
        return if (!_isVpnMode.value) {
            enableVpnMode()
        } else {
            Timber.d("Already in VPN mode")
            NetworkResult.Success
        }
    }
    
    /**
     * تحديث حالة الشبكة بناءً على حالة VPN
     */
    private fun updateNetworkState(vpnState: VpnState) {
        when (vpnState) {
            VpnState.CONNECTED -> {
                if (_isVpnMode.value) {
                    _networkState.value = NetworkState.VPN_CONNECTED
                }
            }
            VpnState.DISCONNECTED -> {
                if (_isVpnMode.value) {
                    _networkState.value = NetworkState.CACHE_ONLY
                }
            }
            VpnState.CONNECTING -> {
                _networkState.value = NetworkState.CONNECTING
            }
            VpnState.DISCONNECTING -> {
                _networkState.value = NetworkState.DISCONNECTING
            }
            VpnState.ERROR -> {
                _networkState.value = NetworkState.ERROR
            }
        }
    }
    
    /**
     * الحصول على حالة الشبكة الحالية
     */
    fun getCurrentNetworkState(): NetworkState {
        return _networkState.value
    }
    
    /**
     * التحقق من وضع VPN
     */
    fun isInVpnMode(): Boolean {
        return _isVpnMode.value
    }
    
    /**
     * التحقق من إمكانية الوصول للإنترنت
     */
    fun hasInternetAccess(): Boolean {
        return when (_networkState.value) {
            NetworkState.VPN_CONNECTED,
            NetworkState.CACHE_ONLY -> true
            else -> false
        }
    }
    
    /**
     * الحصول على إحصائيات الشبكة
     */
    fun getNetworkStatistics(): Flow<NetworkStatistics> {
        return combine(
            vpnManager.vpnStatistics.asFlow(),
            proxyManager.proxyStatistics,
            cacheManager.getCacheStatsFlow()
        ) { vpnStats, proxyStats, cacheStats ->
            NetworkStatistics(
                networkState = _networkState.value,
                isVpnMode = _isVpnMode.value,
                vpnConnections = vpnStats?.activeConnections ?: 0,
                proxyConnections = proxyStats.activeConnections,
                cacheSize = cacheStats.totalSize,
                cacheHitRatio = cacheStats.hitRatio
            )
        }
    }
    
    /**
     * إعادة تشغيل النظام
     */
    suspend fun restartSystem(): NetworkResult {
        return try {
            Timber.d("Restarting network system")
            
            // إيقاف كل شيء
            disableVpnMode()
            delay(2000)
            
            // إعادة التشغيل حسب الوضع المطلوب
            if (_isVpnMode.value) {
                enableVpnMode()
            } else {
                switchToCacheOnlyMode()
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to restart network system")
            NetworkResult.Error(e.message ?: "Unknown error")
        }
    }
    
    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        managerScope.cancel()
    }
}

/**
 * حالات الشبكة
 */
enum class NetworkState {
    DISCONNECTED,
    CONNECTING,
    VPN_CONNECTED,
    CACHE_ONLY,
    DISCONNECTING,
    PERMISSION_REQUIRED,
    ERROR
}

/**
 * نتائج عمليات الشبكة
 */
sealed class NetworkResult {
    object Success : NetworkResult()
    data class PermissionRequired(val intent: android.content.Intent) : NetworkResult()
    data class Error(val message: String) : NetworkResult()
}

/**
 * إحصائيات الشبكة
 */
data class NetworkStatistics(
    val networkState: NetworkState,
    val isVpnMode: Boolean,
    val vpnConnections: Int,
    val proxyConnections: Int,
    val cacheSize: Long,
    val cacheHitRatio: Float
)
