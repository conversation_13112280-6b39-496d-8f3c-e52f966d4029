package com.offlinefirst.internetmanager.di

import android.content.Context
import com.offlinefirst.internetmanager.BuildConfig
import com.offlinefirst.internetmanager.network.CacheInterceptor
import com.offlinefirst.internetmanager.network.LoggingInterceptor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.Cache
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.File
import java.util.concurrent.TimeUnit
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * وحدة Hilt للشبكة
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    /**
     * توفير OkHttp Cache
     */
    @Provides
    @Singleton
    fun provideOkHttpCache(@ApplicationContext context: Context): Cache {
        val cacheDir = File(context.cacheDir, "http_cache")
        val cacheSize = 50L * 1024 * 1024 // 50MB
        return Cache(cacheDir, cacheSize)
    }
    
    /**
     * توفير OkHttpClient للاستخدام العام
     */
    @Provides
    @Singleton
    @GeneralHttpClient
    fun provideGeneralOkHttpClient(
        cache: Cache,
        cacheInterceptor: CacheInterceptor
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .cache(cache)
            .addInterceptor(cacheInterceptor)
            .apply {
                if (BuildConfig.DEBUG) {
                    addInterceptor(LoggingInterceptor())
                }
            }
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    /**
     * توفير OkHttpClient للـ VPN (بدون كاش)
     */
    @Provides
    @Singleton
    @VpnHttpClient
    fun provideVpnOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .apply {
                if (BuildConfig.DEBUG) {
                    addInterceptor(LoggingInterceptor())
                }
            }
            .connectTimeout(15, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(15, TimeUnit.SECONDS)
            .build()
    }
    
    /**
     * توفير Retrofit للاستخدام العام
     */
    @Provides
    @Singleton
    fun provideRetrofit(@GeneralHttpClient okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://api.example.com/") // يمكن تغييرها حسب الحاجة
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
}

/**
 * مؤهلات للتمييز بين أنواع OkHttpClient المختلفة
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class GeneralHttpClient

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class VpnHttpClient
