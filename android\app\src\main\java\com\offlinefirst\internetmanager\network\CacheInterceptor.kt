package com.offlinefirst.internetmanager.network

import com.offlinefirst.internetmanager.data.database.dao.CacheDao
import com.offlinefirst.internetmanager.data.database.dao.NetworkStatsDao
import com.offlinefirst.internetmanager.data.model.CacheEntry
import kotlinx.coroutines.runBlocking
import okhttp3.*
import okio.Buffer
import timber.log.Timber
import java.io.File
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * معترض OkHttp للتعامل مع الكاش المخصص
 */
@Singleton
class CacheInterceptor @Inject constructor(
    private val cacheDao: CacheDao,
    private val networkStatsDao: NetworkStatsDao
) : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val url = request.url.toString()
        
        return try {
            // البحث في الكاش أولاً
            val cachedEntry = runBlocking { cacheDao.getCachedResponse(url) }
            
            if (cachedEntry != null && !cachedEntry.isExpired()) {
                Timber.d("Cache HIT for: $url")
                
                // تحديث إحصائيات Cache Hit
                runBlocking {
                    cacheDao.updateAccessStats(url)
                    networkStatsDao.incrementCacheHit()
                    networkStatsDao.addDataSaved(cachedEntry.size)
                }
                
                // إنشاء استجابة من الكاش
                createCachedResponse(request, cachedEntry)
            } else {
                Timber.d("Cache MISS for: $url")
                
                // جلب من الشبكة
                val response = chain.proceed(request)
                
                // تحديث إحصائيات Cache Miss
                runBlocking {
                    networkStatsDao.incrementCacheMiss()
                    networkStatsDao.addDataUsed(response.body?.contentLength() ?: 0)
                }
                
                // حفظ في الكاش إذا كانت الاستجابة ناجحة
                if (response.isSuccessful && shouldCache(request, response)) {
                    runBlocking {
                        saveToCache(url, response)
                    }
                }
                
                response
            }
        } catch (e: Exception) {
            Timber.e(e, "Error in cache interceptor for: $url")
            // في حالة الخطأ، المتابعة بالطلب العادي
            chain.proceed(request)
        }
    }
    
    /**
     * إنشاء استجابة من الكاش
     */
    private fun createCachedResponse(request: Request, cacheEntry: CacheEntry): Response {
        val cacheFile = File(cacheEntry.filePath ?: "")
        
        if (!cacheFile.exists()) {
            throw IOException("Cache file not found: ${cacheEntry.filePath}")
        }
        
        val responseBody = ResponseBody.create(
            MediaType.parse(cacheEntry.contentType ?: "application/octet-stream"),
            cacheFile.readBytes()
        )
        
        return Response.Builder()
            .request(request)
            .protocol(Protocol.HTTP_1_1)
            .code(cacheEntry.responseCode)
            .message("OK")
            .body(responseBody)
            .addHeader("X-Cache", "HIT")
            .addHeader("X-Cache-Date", cacheEntry.timestamp.toString())
            .apply {
                // إضافة الرؤوس المحفوظة
                cacheEntry.headers?.let { headersJson ->
                    try {
                        // تحليل JSON للرؤوس وإضافتها
                        // يمكن تحسين هذا باستخدام مكتبة JSON
                    } catch (e: Exception) {
                        Timber.w(e, "Failed to parse cached headers")
                    }
                }
            }
            .build()
    }
    
    /**
     * حفظ الاستجابة في الكاش
     */
    private suspend fun saveToCache(url: String, response: Response) {
        try {
            val responseBody = response.body ?: return
            val contentLength = responseBody.contentLength()
            
            if (contentLength > MAX_CACHE_ENTRY_SIZE) {
                Timber.d("Response too large to cache: $contentLength bytes")
                return
            }
            
            // قراءة محتوى الاستجابة
            val source = responseBody.source()
            val buffer = Buffer()
            source.readAll(buffer)
            val content = buffer.readByteArray()
            
            // إنشاء ملف الكاش
            val cacheDir = File(getCacheDirectory(), "responses")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }
            
            val cacheFile = File(cacheDir, generateCacheFileName(url))
            cacheFile.writeBytes(content)
            
            // إنشاء إدخال الكاش
            val cacheEntry = CacheEntry(
                url = url,
                size = content.size.toLong(),
                contentType = response.header("Content-Type"),
                etag = response.header("ETag"),
                lastModified = response.header("Last-Modified"),
                expiryTime = calculateExpiryTime(response),
                filePath = cacheFile.absolutePath,
                responseCode = response.code,
                headers = serializeHeaders(response.headers)
            )
            
            // حفظ في قاعدة البيانات
            cacheDao.insertCacheEntry(cacheEntry)
            
            Timber.d("Cached response for: $url (${content.size} bytes)")
            
        } catch (e: Exception) {
            Timber.e(e, "Failed to save response to cache: $url")
        }
    }
    
    /**
     * التحقق من إمكانية تخزين الطلب في الكاش
     */
    private fun shouldCache(request: Request, response: Response): Boolean {
        // لا نخزن طلبات POST, PUT, DELETE
        if (request.method != "GET") return false
        
        // لا نخزن الاستجابات الخاطئة
        if (!response.isSuccessful) return false
        
        // لا نخزن الاستجابات الكبيرة جداً
        val contentLength = response.body?.contentLength() ?: 0
        if (contentLength > MAX_CACHE_ENTRY_SIZE) return false
        
        // التحقق من رؤوس Cache-Control
        val cacheControl = response.header("Cache-Control")
        if (cacheControl?.contains("no-cache") == true || 
            cacheControl?.contains("no-store") == true) {
            return false
        }
        
        return true
    }
    
    /**
     * حساب وقت انتهاء الصلاحية
     */
    private fun calculateExpiryTime(response: Response): Long? {
        // البحث عن Cache-Control max-age
        val cacheControl = response.header("Cache-Control")
        if (cacheControl != null) {
            val maxAgeRegex = "max-age=(\\d+)".toRegex()
            val matchResult = maxAgeRegex.find(cacheControl)
            if (matchResult != null) {
                val maxAge = matchResult.groupValues[1].toLongOrNull()
                if (maxAge != null) {
                    return System.currentTimeMillis() + (maxAge * 1000)
                }
            }
        }
        
        // البحث عن Expires header
        val expires = response.header("Expires")
        if (expires != null) {
            try {
                // تحليل تاريخ Expires
                // يمكن استخدام SimpleDateFormat هنا
            } catch (e: Exception) {
                Timber.w(e, "Failed to parse Expires header: $expires")
            }
        }
        
        // افتراضي: 24 ساعة
        return System.currentTimeMillis() + (24 * 60 * 60 * 1000)
    }
    
    /**
     * تسلسل رؤوس الاستجابة
     */
    private fun serializeHeaders(headers: Headers): String {
        // تحويل الرؤوس إلى JSON
        // يمكن تحسين هذا باستخدام مكتبة JSON
        return headers.toString()
    }
    
    /**
     * إنشاء اسم ملف الكاش
     */
    private fun generateCacheFileName(url: String): String {
        return url.hashCode().toString() + ".cache"
    }
    
    /**
     * الحصول على مجلد الكاش
     */
    private fun getCacheDirectory(): File {
        // سيتم تمرير هذا من خلال الحقن
        return File("/cache") // مؤقت
    }
    
    companion object {
        private const val MAX_CACHE_ENTRY_SIZE = 10 * 1024 * 1024L // 10MB
    }
}
