package com.offlinefirst.internetmanager.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.offlinefirst.internetmanager.data.model.CacheEntry

/**
 * DAO للتعامل مع بيانات الكاش
 */
@Dao
interface CacheDao {
    
    /**
     * البحث عن مورد في الكاش
     */
    @Query("SELECT * FROM cache_entries WHERE url = :url LIMIT 1")
    suspend fun getCachedResponse(url: String): CacheEntry?
    
    /**
     * إدراج أو تحديث إدخال كاش
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCacheEntry(entry: CacheEntry)
    
    /**
     * إدراج عدة إدخالات كاش
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCacheEntries(entries: List<CacheEntry>)
    
    /**
     * تحديث إدخال كاش
     */
    @Update
    suspend fun updateCacheEntry(entry: CacheEntry)
    
    /**
     * حذف إدخال كاش
     */
    @Delete
    suspend fun deleteCacheEntry(entry: CacheEntry)
    
    /**
     * حذف إدخال كاش بالرابط
     */
    @Query("DELETE FROM cache_entries WHERE url = :url")
    suspend fun deleteCacheEntryByUrl(url: String)
    
    /**
     * حذف الإدخالات المنتهية الصلاحية
     */
    @Query("DELETE FROM cache_entries WHERE expiryTime IS NOT NULL AND expiryTime < :currentTime")
    suspend fun deleteExpiredEntries(currentTime: Long = System.currentTimeMillis())
    
    /**
     * حذف الإدخالات القديمة (أكثر من عدد ساعات محدد)
     */
    @Query("DELETE FROM cache_entries WHERE timestamp < :expiredTime")
    suspend fun deleteOldEntries(expiredTime: Long)
    
    /**
     * حذف جميع إدخالات الكاش
     */
    @Query("DELETE FROM cache_entries")
    suspend fun deleteAllEntries()
    
    /**
     * الحصول على إجمالي حجم الكاش
     */
    @Query("SELECT COALESCE(SUM(size), 0) FROM cache_entries")
    suspend fun getTotalCacheSize(): Long
    
    /**
     * الحصول على عدد إدخالات الكاش
     */
    @Query("SELECT COUNT(*) FROM cache_entries")
    suspend fun getCacheEntryCount(): Int
    
    /**
     * الحصول على أقدم إدخال
     */
    @Query("SELECT * FROM cache_entries ORDER BY timestamp ASC LIMIT 1")
    suspend fun getOldestEntry(): CacheEntry?
    
    /**
     * الحصول على أحدث إدخال
     */
    @Query("SELECT * FROM cache_entries ORDER BY timestamp DESC LIMIT 1")
    suspend fun getNewestEntry(): CacheEntry?
    
    /**
     * الحصول على الإدخالات الأكثر استخداماً
     */
    @Query("SELECT * FROM cache_entries ORDER BY accessCount DESC LIMIT :limit")
    suspend fun getMostAccessedEntries(limit: Int = 10): List<CacheEntry>
    
    /**
     * الحصول على الإدخالات الأقل استخداماً
     */
    @Query("SELECT * FROM cache_entries ORDER BY accessCount ASC LIMIT :limit")
    suspend fun getLeastAccessedEntries(limit: Int = 10): List<CacheEntry>
    
    /**
     * الحصول على الإدخالات حسب الحجم (الأكبر أولاً)
     */
    @Query("SELECT * FROM cache_entries ORDER BY size DESC LIMIT :limit")
    suspend fun getLargestEntries(limit: Int = 10): List<CacheEntry>
    
    /**
     * البحث في الإدخالات بالرابط
     */
    @Query("SELECT * FROM cache_entries WHERE url LIKE '%' || :searchTerm || '%'")
    suspend fun searchEntries(searchTerm: String): List<CacheEntry>
    
    /**
     * الحصول على الإدخالات حسب نوع المحتوى
     */
    @Query("SELECT * FROM cache_entries WHERE contentType LIKE :contentType || '%'")
    suspend fun getEntriesByContentType(contentType: String): List<CacheEntry>
    
    /**
     * الحصول على الإدخالات في فترة زمنية محددة
     */
    @Query("SELECT * FROM cache_entries WHERE timestamp BETWEEN :startTime AND :endTime")
    suspend fun getEntriesInTimeRange(startTime: Long, endTime: Long): List<CacheEntry>
    
    /**
     * تحديث إحصائيات الوصول
     */
    @Query("UPDATE cache_entries SET accessCount = accessCount + 1, lastAccessTime = :accessTime WHERE url = :url")
    suspend fun updateAccessStats(url: String, accessTime: Long = System.currentTimeMillis())
    
    /**
     * مراقبة تغييرات الكاش
     */
    @Query("SELECT * FROM cache_entries ORDER BY timestamp DESC")
    fun observeAllEntries(): Flow<List<CacheEntry>>
    
    /**
     * مراقبة حجم الكاش
     */
    @Query("SELECT COALESCE(SUM(size), 0) FROM cache_entries")
    fun observeTotalCacheSize(): Flow<Long>
    
    /**
     * مراقبة عدد الإدخالات
     */
    @Query("SELECT COUNT(*) FROM cache_entries")
    fun observeCacheEntryCount(): Flow<Int>
    
    /**
     * الحصول على إحصائيات الكاش
     */
    @Query("""
        SELECT 
            COUNT(*) as totalEntries,
            COALESCE(SUM(size), 0) as totalSize,
            COALESCE(AVG(size), 0) as averageSize,
            COALESCE(SUM(accessCount), 0) as totalAccess,
            MIN(timestamp) as oldestTimestamp,
            MAX(timestamp) as newestTimestamp
        FROM cache_entries
    """)
    suspend fun getCacheStatistics(): CacheStatistics
    
    /**
     * تنظيف الكاش بناءً على الحجم (حذف الأقل استخداماً)
     */
    @Query("""
        DELETE FROM cache_entries 
        WHERE id IN (
            SELECT id FROM cache_entries 
            ORDER BY accessCount ASC, lastAccessTime ASC 
            LIMIT :count
        )
    """)
    suspend fun cleanupLeastUsedEntries(count: Int)
}

/**
 * نموذج بيانات إحصائيات الكاش
 */
data class CacheStatistics(
    val totalEntries: Int,
    val totalSize: Long,
    val averageSize: Double,
    val totalAccess: Long,
    val oldestTimestamp: Long?,
    val newestTimestamp: Long?
)
