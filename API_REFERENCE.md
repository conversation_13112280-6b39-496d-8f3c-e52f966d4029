# مرجع API والدوال الأساسية
## API Reference & Core Functions

## 🔧 الدوال الأساسية (Core Functions)

### 1. إدارة VPN (VPN Management)

#### `startVpnService()`
```kotlin
// Android
suspend fun startVpnService(): Result<Boolean> {
    return try {
        val intent = VpnService.prepare(context)
        if (intent != null) {
            // طلب إذن VPN من المستخدم
            return Result.failure(VpnPermissionRequiredException())
        }
        
        val serviceIntent = Intent(context, LocalVpnService::class.java)
        context.startForegroundService(serviceIntent)
        
        Result.success(true)
    } catch (e: Exception) {
        Result.failure(e)
    }
}
```

```swift
// iOS
func startVpnService() async throws -> Bool {
    let manager = NETunnelProviderManager()
    
    do {
        try await manager.loadFromPreferences()
        
        if manager.connection.status != .connected {
            try manager.connection.startVPNTunnel()
            return true
        }
        return false
    } catch {
        throw VpnServiceError.failedToStart(error)
    }
}
```

#### `stopVpnService()`
```kotlin
// Android
suspend fun stopVpnService(): Result<Boolean> {
    return try {
        val serviceIntent = Intent(context, LocalVpnService::class.java)
        context.stopService(serviceIntent)
        Result.success(true)
    } catch (e: Exception) {
        Result.failure(e)
    }
}
```

```swift
// iOS
func stopVpnService() async throws -> Bool {
    let manager = NETunnelProviderManager()
    
    do {
        try await manager.loadFromPreferences()
        manager.connection.stopVPNTunnel()
        return true
    } catch {
        throw VpnServiceError.failedToStop(error)
    }
}
```

### 2. إدارة الكاش (Cache Management)

#### `fetchFromCache(url: String)`
```kotlin
// Android
suspend fun fetchFromCache(url: String): CacheResult {
    return try {
        val cacheEntry = cacheDao.getCachedResponse(url)
        
        if (cacheEntry != null && !isExpired(cacheEntry)) {
            val file = File(cacheDir, cacheEntry.id)
            if (file.exists()) {
                CacheResult.Hit(
                    data = file.readBytes(),
                    contentType = cacheEntry.contentType,
                    timestamp = cacheEntry.timestamp
                )
            } else {
                CacheResult.Miss
            }
        } else {
            CacheResult.Miss
        }
    } catch (e: Exception) {
        CacheResult.Error(e)
    }
}
```

```swift
// iOS
func fetchFromCache(url: String) async throws -> CacheResult {
    let request = NSFetchRequest<CacheEntry>(entityName: "CacheEntry")
    request.predicate = NSPredicate(format: "url == %@", url)
    
    do {
        let entries = try context.fetch(request)
        
        if let entry = entries.first, !isExpired(entry) {
            if let data = entry.data {
                return .hit(data: data, contentType: entry.contentType, timestamp: entry.timestamp)
            }
        }
        
        return .miss
    } catch {
        return .error(error)
    }
}
```

#### `clearCache()`
```kotlin
// Android
suspend fun clearCache(): Result<Long> {
    return try {
        val deletedSize = cacheDao.getTotalCacheSize()
        cacheDao.deleteAllEntries()
        
        // حذف الملفات المخزنة
        cacheDir.listFiles()?.forEach { it.delete() }
        
        Result.success(deletedSize)
    } catch (e: Exception) {
        Result.failure(e)
    }
}
```

### 3. إدارة Prefetch (Prefetch Management)

#### `enqueuePrefetch()`
```kotlin
// Android
fun enqueuePrefetch(immediate: Boolean = false): UUID {
    val constraints = Constraints.Builder()
        .setRequiredNetworkType(NetworkType.UNMETERED)
        .setRequiresBatteryNotLow(true)
        .build()
    
    val workRequest = if (immediate) {
        OneTimeWorkRequestBuilder<PrefetchWorker>()
            .setConstraints(constraints)
            .build()
    } else {
        PeriodicWorkRequestBuilder<PrefetchWorker>(6, TimeUnit.HOURS)
            .setConstraints(constraints)
            .build()
    }
    
    workManager.enqueue(workRequest)
    return workRequest.id
}
```

```swift
// iOS
func enqueuePrefetch(immediate: Bool = false) throws -> String {
    let identifier = "com.app.prefetch.\(UUID().uuidString)"
    
    if immediate {
        let request = BGProcessingTaskRequest(identifier: identifier)
        request.requiresNetworkConnectivity = true
        request.requiresExternalPower = false
        
        try BGTaskScheduler.shared.submit(request)
    } else {
        let request = BGAppRefreshTaskRequest(identifier: identifier)
        try BGTaskScheduler.shared.submit(request)
    }
    
    return identifier
}
```

#### `cancelPrefetch(taskId: String)`
```kotlin
// Android
fun cancelPrefetch(taskId: UUID) {
    workManager.cancelWorkById(taskId)
}
```

```swift
// iOS
func cancelPrefetch(taskId: String) {
    BGTaskScheduler.shared.cancel(taskRequestWithIdentifier: taskId)
}
```

### 4. كشف الشبكة (Network Detection)

#### `getCurrentNetworkType()`
```kotlin
// Android
fun getCurrentNetworkType(): NetworkType {
    val activeNetwork = connectivityManager.activeNetwork
    val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
    
    return when {
        networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> {
            if (isHomeWifi(activeNetwork)) NetworkType.HOME_WIFI else NetworkType.OTHER_WIFI
        }
        networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> {
            NetworkType.MOBILE_DATA
        }
        else -> NetworkType.NONE
    }
}
```

```swift
// iOS
func getCurrentNetworkType() -> NetworkType {
    let monitor = NWPathMonitor()
    let path = monitor.currentPath
    
    if path.usesInterfaceType(.wifi) {
        return isHomeWifi(path) ? .homeWifi : .otherWifi
    } else if path.usesInterfaceType(.cellular) {
        return .mobileData
    } else {
        return .none
    }
}
```

#### `isHomeWifi(network: Network)`
```kotlin
// Android
private fun isHomeWifi(network: Network): Boolean {
    val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
    val wifiInfo = wifiManager.connectionInfo
    val currentSSID = wifiInfo.ssid?.removeSurrounding("\"")
    
    val homeSSIDs = getHomeWifiSSIDs() // من الإعدادات
    return homeSSIDs.contains(currentSSID)
}
```

### 5. إحصائيات التطبيق (App Statistics)

#### `getDataSavingsStats()`
```kotlin
// Android
suspend fun getDataSavingsStats(): DataSavingsStats {
    val totalCacheSize = cacheDao.getTotalCacheSize()
    val cacheHitCount = statsDao.getCacheHitCount()
    val cacheMissCount = statsDao.getCacheMissCount()
    val totalRequests = cacheHitCount + cacheMissCount
    
    val hitRatio = if (totalRequests > 0) {
        (cacheHitCount.toDouble() / totalRequests * 100).toInt()
    } else 0
    
    return DataSavingsStats(
        totalCacheSize = totalCacheSize,
        hitRatio = hitRatio,
        totalRequests = totalRequests,
        dataSaved = calculateDataSaved()
    )
}
```

#### `getCacheStats()`
```kotlin
// Android
suspend fun getCacheStats(): CacheStats {
    return CacheStats(
        totalSize = cacheDao.getTotalCacheSize(),
        entryCount = cacheDao.getCacheEntryCount(),
        oldestEntry = cacheDao.getOldestEntry()?.timestamp,
        newestEntry = cacheDao.getNewestEntry()?.timestamp,
        hitRatio = calculateHitRatio()
    )
}
```

## 📊 نماذج البيانات (Data Models)

### CacheResult
```kotlin
sealed class CacheResult {
    data class Hit(
        val data: ByteArray,
        val contentType: String?,
        val timestamp: Long
    ) : CacheResult()
    
    object Miss : CacheResult()
    
    data class Error(val exception: Exception) : CacheResult()
}
```

### NetworkType
```kotlin
enum class NetworkType {
    HOME_WIFI,
    OTHER_WIFI,
    MOBILE_DATA,
    NONE
}
```

### DataSavingsStats
```kotlin
data class DataSavingsStats(
    val totalCacheSize: Long,
    val hitRatio: Int, // نسبة مئوية
    val totalRequests: Long,
    val dataSaved: Long // بالبايت
)
```

### PrefetchResource
```kotlin
data class PrefetchResource(
    val url: String,
    val priority: Int = 0,
    val category: String,
    val isEnabled: Boolean = true,
    val lastFetched: Long? = null
)
```

## 🔄 Callbacks والأحداث (Events)

### NetworkStateCallback
```kotlin
interface NetworkStateCallback {
    fun onNetworkChanged(networkType: NetworkType)
    fun onVpnStateChanged(isConnected: Boolean)
    fun onDataSavingsUpdated(stats: DataSavingsStats)
}
```

### CacheEventListener
```kotlin
interface CacheEventListener {
    fun onCacheHit(url: String, size: Long)
    fun onCacheMiss(url: String)
    fun onCacheCleared(freedSpace: Long)
    fun onCacheError(url: String, error: Exception)
}
```

### PrefetchEventListener
```kotlin
interface PrefetchEventListener {
    fun onPrefetchStarted(resourceCount: Int)
    fun onPrefetchProgress(completed: Int, total: Int)
    fun onPrefetchCompleted(successCount: Int, failureCount: Int)
    fun onPrefetchError(error: Exception)
}
```

## ⚙️ إعدادات التطبيق (App Configuration)

### AppConfig
```kotlin
data class AppConfig(
    val homeWifiSSIDs: List<String> = emptyList(),
    val maxCacheSize: Long = 100 * 1024 * 1024, // 100MB
    val prefetchInterval: Long = 6 * 60 * 60 * 1000, // 6 ساعات
    val cacheExpiryTime: Long = 24 * 60 * 60 * 1000, // 24 ساعة
    val enablePrefetch: Boolean = true,
    val enableVpn: Boolean = true,
    val prefetchOnlyOnWifi: Boolean = true,
    val prefetchOnlyWhenCharging: Boolean = false
)
```

## 🚨 معالجة الأخطاء (Error Handling)

### AppException
```kotlin
sealed class AppException : Exception() {
    object VpnPermissionRequiredException : AppException()
    object NetworkNotAvailableException : AppException()
    data class CacheException(val cause: Throwable) : AppException()
    data class VpnServiceException(val cause: Throwable) : AppException()
    data class PrefetchException(val cause: Throwable) : AppException()
}
```
