package com.offlinefirst.internetmanager.proxy

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير البروكسي للتحكم في البروكسي المحلي
 */
@Singleton
class ProxyManager @Inject constructor(
    private val localProxy: LocalProxy
) {
    
    private val _proxyState = MutableStateFlow(ProxyState.STOPPED)
    val proxyState: StateFlow<ProxyState> = _proxyState.asStateFlow()
    
    private val _proxyStatistics = MutableStateFlow(ProxyStatistics(false, 0, 0))
    val proxyStatistics: StateFlow<ProxyStatistics> = _proxyStatistics.asStateFlow()
    
    private val managerScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    /**
     * بدء البروكسي المحلي
     */
    suspend fun startProxy(): ProxyResult {
        return withContext(Dispatchers.Main) {
            try {
                if (_proxyState.value == ProxyState.RUNNING) {
                    Timber.d("Proxy already running")
                    return@withContext ProxyResult.Success
                }
                
                Timber.d("Starting local proxy")
                _proxyState.value = ProxyState.STARTING
                
                val success = localProxy.startProxy()
                
                if (success) {
                    _proxyState.value = ProxyState.RUNNING
                    startStatisticsMonitoring()
                    Timber.i("Local proxy started successfully")
                    ProxyResult.Success
                } else {
                    _proxyState.value = ProxyState.ERROR
                    ProxyResult.Error("Failed to start proxy")
                }
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to start proxy")
                _proxyState.value = ProxyState.ERROR
                ProxyResult.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * إيقاف البروكسي المحلي
     */
    suspend fun stopProxy(): ProxyResult {
        return withContext(Dispatchers.Main) {
            try {
                if (_proxyState.value == ProxyState.STOPPED) {
                    Timber.d("Proxy already stopped")
                    return@withContext ProxyResult.Success
                }
                
                Timber.d("Stopping local proxy")
                _proxyState.value = ProxyState.STOPPING
                
                localProxy.stopProxy()
                stopStatisticsMonitoring()
                
                _proxyState.value = ProxyState.STOPPED
                Timber.i("Local proxy stopped successfully")
                ProxyResult.Success
                
            } catch (e: Exception) {
                Timber.e(e, "Failed to stop proxy")
                _proxyState.value = ProxyState.ERROR
                ProxyResult.Error(e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * التحقق من حالة تشغيل البروكسي
     */
    fun isProxyRunning(): Boolean {
        return _proxyState.value == ProxyState.RUNNING
    }
    
    /**
     * الحصول على حالة البروكسي الحالية
     */
    fun getCurrentProxyState(): ProxyState {
        return _proxyState.value
    }
    
    /**
     * بدء مراقبة إحصائيات البروكسي
     */
    private fun startStatisticsMonitoring() {
        managerScope.launch {
            while (_proxyState.value == ProxyState.RUNNING) {
                try {
                    val stats = localProxy.getProxyStatistics()
                    _proxyStatistics.value = stats
                    
                    delay(2000) // تحديث كل ثانيتين
                } catch (e: Exception) {
                    Timber.e(e, "Error updating proxy statistics")
                    delay(5000) // انتظار أطول في حالة الخطأ
                }
            }
        }
    }
    
    /**
     * إيقاف مراقبة الإحصائيات
     */
    private fun stopStatisticsMonitoring() {
        // سيتم إيقاف المراقبة تلقائياً عند تغيير الحالة
        _proxyStatistics.value = ProxyStatistics(false, 0, 0)
    }
    
    /**
     * إعادة تشغيل البروكسي
     */
    suspend fun restartProxy(): ProxyResult {
        return try {
            stopProxy()
            delay(1000) // انتظار قصير
            startProxy()
        } catch (e: Exception) {
            Timber.e(e, "Failed to restart proxy")
            ProxyResult.Error(e.message ?: "Unknown error")
        }
    }
    
    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        managerScope.cancel()
    }
}

/**
 * حالات البروكسي
 */
enum class ProxyState {
    STOPPED,
    STARTING,
    RUNNING,
    STOPPING,
    ERROR
}

/**
 * نتائج عمليات البروكسي
 */
sealed class ProxyResult {
    object Success : ProxyResult()
    data class Error(val message: String) : ProxyResult()
}

/**
 * معلومات حالة البروكسي
 */
data class ProxyStateInfo(
    val state: ProxyState,
    val isRunning: Boolean,
    val startedSince: Long? = null,
    val lastError: String? = null,
    val activeConnections: Int = 0
) {
    val isActive: Boolean
        get() = state == ProxyState.RUNNING && isRunning
}
