package com.offlinefirst.internetmanager.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.offlinefirst.internetmanager.ui.screen.MainScreen
import com.offlinefirst.internetmanager.ui.theme.OfflineFirstInternetManagerTheme
import com.offlinefirst.internetmanager.ui.viewmodel.MainViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * النشاط الرئيسي للتطبيق
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    private val viewModel: MainViewModel by viewModels()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            OfflineFirstInternetManagerTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen(viewModel = viewModel)
                }
            }
        }
    }
}
