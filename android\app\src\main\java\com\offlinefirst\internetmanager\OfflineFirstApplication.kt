package com.offlinefirst.internetmanager

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber
import javax.inject.Inject

/**
 * فئة التطبيق الرئيسية مع إعداد Hilt و WorkManager
 */
@HiltAndroidApp
class OfflineFirstApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    override fun onCreate() {
        super.onCreate()
        
        // إعداد Timber للسجلات
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
        
        // إنشاء قنوات الإشعارات
        createNotificationChannels()
        
        Timber.d("OfflineFirstApplication initialized")
    }

    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .setMinimumLoggingLevel(if (BuildConfig.DEBUG) android.util.Log.DEBUG else android.util.Log.ERROR)
            .build()
    }

    /**
     * إنشاء قنوات الإشعارات المطلوبة للتطبيق
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NotificationManager::class.java)
            
            // قناة خدمة VPN
            val vpnChannel = NotificationChannel(
                CHANNEL_VPN_SERVICE,
                getString(R.string.notification_channel_vpn_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.notification_channel_vpn_description)
                setShowBadge(false)
            }
            
            // قناة مراقبة الشبكة
            val networkChannel = NotificationChannel(
                CHANNEL_NETWORK_MONITOR,
                getString(R.string.notification_channel_network_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.notification_channel_network_description)
                setShowBadge(false)
            }
            
            // قناة Prefetch
            val prefetchChannel = NotificationChannel(
                CHANNEL_PREFETCH,
                getString(R.string.notification_channel_prefetch_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.notification_channel_prefetch_description)
                setShowBadge(false)
            }
            
            // قناة التنبيهات العامة
            val generalChannel = NotificationChannel(
                CHANNEL_GENERAL,
                getString(R.string.notification_channel_general_name),
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = getString(R.string.notification_channel_general_description)
            }
            
            notificationManager.createNotificationChannels(
                listOf(vpnChannel, networkChannel, prefetchChannel, generalChannel)
            )
        }
    }

    companion object {
        // معرفات قنوات الإشعارات
        const val CHANNEL_VPN_SERVICE = "vpn_service_channel"
        const val CHANNEL_NETWORK_MONITOR = "network_monitor_channel"
        const val CHANNEL_PREFETCH = "prefetch_channel"
        const val CHANNEL_GENERAL = "general_channel"
        
        // معرفات الإشعارات
        const val NOTIFICATION_VPN_SERVICE = 1001
        const val NOTIFICATION_NETWORK_MONITOR = 1002
        const val NOTIFICATION_PREFETCH = 1003
    }
}
