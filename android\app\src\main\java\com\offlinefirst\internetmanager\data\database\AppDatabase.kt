package com.offlinefirst.internetmanager.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.offlinefirst.internetmanager.data.database.dao.CacheDao
import com.offlinefirst.internetmanager.data.database.dao.NetworkStatsDao
import com.offlinefirst.internetmanager.data.database.dao.PrefetchResourceDao
import com.offlinefirst.internetmanager.data.database.dao.AppSettingsDao
import com.offlinefirst.internetmanager.data.model.CacheEntry
import com.offlinefirst.internetmanager.data.model.NetworkStats
import com.offlinefirst.internetmanager.data.model.PrefetchResource
import com.offlinefirst.internetmanager.data.model.AppSettings

/**
 * قاعدة البيانات الرئيسية للتطبيق
 */
@Database(
    entities = [
        CacheEntry::class,
        PrefetchResource::class,
        NetworkStats::class,
        AppSettings::class
    ],
    version = 1,
    exportSchema = true
)
@TypeConverters(Converters::class)
abstract class AppDatabase : RoomDatabase() {
    
    abstract fun cacheDao(): CacheDao
    abstract fun prefetchResourceDao(): PrefetchResourceDao
    abstract fun networkStatsDao(): NetworkStatsDao
    abstract fun appSettingsDao(): AppSettingsDao
    
    companion object {
        const val DATABASE_NAME = "offline_first_database"
        
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                .addMigrations(/* إضافة migrations هنا عند الحاجة */)
                .fallbackToDestructiveMigration() // للتطوير فقط
                .build()
                
                INSTANCE = instance
                instance
            }
        }
    }
}
