package com.offlinefirst.internetmanager.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import com.offlinefirst.internetmanager.network.NetworkMonitorServiceHelper
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * مستقبل تغييرات الشبكة
 */
@AndroidEntryPoint
class NetworkChangeReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        try {
            when (intent.action) {
                ConnectivityManager.CONNECTIVITY_ACTION -> {
                    handleConnectivityChange(context, intent)
                }
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    handleWifiStateChange(context, intent)
                }
                WifiManager.NETWORK_STATE_CHANGED_ACTION -> {
                    handleWifiNetworkStateChange(context, intent)
                }
                else -> {
                    Timber.d("Received unknown network action: ${intent.action}")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Error handling network change: ${intent.action}")
        }
    }
    
    /**
     * معالجة تغيير الاتصال
     */
    private fun handleConnectivityChange(context: Context, intent: Intent) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val activeNetwork = connectivityManager.activeNetworkInfo
        
        if (activeNetwork != null && activeNetwork.isConnected) {
            when (activeNetwork.type) {
                ConnectivityManager.TYPE_WIFI -> {
                    Timber.d("WiFi connected")
                    ensureMonitoringServiceRunning(context)
                }
                ConnectivityManager.TYPE_MOBILE -> {
                    Timber.d("Mobile data connected")
                    ensureMonitoringServiceRunning(context)
                }
                else -> {
                    Timber.d("Other network type connected: ${activeNetwork.type}")
                    ensureMonitoringServiceRunning(context)
                }
            }
        } else {
            Timber.d("Network disconnected")
            // لا نوقف الخدمة عند انقطاع الشبكة لأننا قد نحتاج للكاش
        }
    }
    
    /**
     * معالجة تغيير حالة الواي-فاي
     */
    private fun handleWifiStateChange(context: Context, intent: Intent) {
        val wifiState = intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, WifiManager.WIFI_STATE_UNKNOWN)
        
        when (wifiState) {
            WifiManager.WIFI_STATE_ENABLED -> {
                Timber.d("WiFi enabled")
                ensureMonitoringServiceRunning(context)
            }
            WifiManager.WIFI_STATE_DISABLED -> {
                Timber.d("WiFi disabled")
                // الخدمة ستتعامل مع هذا التغيير
            }
            WifiManager.WIFI_STATE_ENABLING -> {
                Timber.d("WiFi enabling")
            }
            WifiManager.WIFI_STATE_DISABLING -> {
                Timber.d("WiFi disabling")
            }
            else -> {
                Timber.d("WiFi state unknown: $wifiState")
            }
        }
    }
    
    /**
     * معالجة تغيير حالة شبكة الواي-فاي
     */
    private fun handleWifiNetworkStateChange(context: Context, intent: Intent) {
        val networkInfo = intent.getParcelableExtra<NetworkInfo>(WifiManager.EXTRA_NETWORK_INFO)
        
        if (networkInfo != null) {
            when (networkInfo.state) {
                NetworkInfo.State.CONNECTED -> {
                    Timber.d("WiFi network connected")
                    ensureMonitoringServiceRunning(context)
                }
                NetworkInfo.State.DISCONNECTED -> {
                    Timber.d("WiFi network disconnected")
                }
                NetworkInfo.State.CONNECTING -> {
                    Timber.d("WiFi network connecting")
                }
                NetworkInfo.State.DISCONNECTING -> {
                    Timber.d("WiFi network disconnecting")
                }
                else -> {
                    Timber.d("WiFi network state: ${networkInfo.state}")
                }
            }
        }
    }
    
    /**
     * التأكد من تشغيل خدمة المراقبة
     */
    private fun ensureMonitoringServiceRunning(context: Context) {
        try {
            // بدء خدمة مراقبة الشبكة إذا لم تكن تعمل
            NetworkMonitorServiceHelper.startService(context)
            Timber.d("Network monitoring service started/ensured")
        } catch (e: Exception) {
            Timber.e(e, "Failed to start network monitoring service")
        }
    }
}
