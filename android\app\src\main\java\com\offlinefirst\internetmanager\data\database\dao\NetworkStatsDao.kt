package com.offlinefirst.internetmanager.data.database.dao

import androidx.room.*
import kotlinx.coroutines.flow.Flow
import com.offlinefirst.internetmanager.data.model.NetworkStats

/**
 * DAO للتعامل مع إحصائيات الشبكة
 */
@Dao
interface NetworkStatsDao {
    
    /**
     * الحصول على إحصائيات يوم محدد
     */
    @Query("SELECT * FROM network_stats WHERE date = :date LIMIT 1")
    suspend fun getStatsForDate(date: String): NetworkStats?
    
    /**
     * الحصول على إحصائيات اليوم الحالي
     */
    @Query("SELECT * FROM network_stats WHERE date = date('now') LIMIT 1")
    suspend fun getTodayStats(): NetworkStats?
    
    /**
     * الحصول على إحصائيات فترة زمنية
     */
    @Query("SELECT * FROM network_stats WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    suspend fun getStatsInRange(startDate: String, endDate: String): List<NetworkStats>
    
    /**
     * الحصول على آخر N أيام من الإحصائيات
     */
    @Query("SELECT * FROM network_stats ORDER BY date DESC LIMIT :days")
    suspend fun getLastDaysStats(days: Int): List<NetworkStats>
    
    /**
     * إدراج أو تحديث إحصائيات
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStats(stats: NetworkStats)
    
    /**
     * تحديث إحصائيات
     */
    @Update
    suspend fun updateStats(stats: NetworkStats)
    
    /**
     * حذف إحصائيات يوم محدد
     */
    @Query("DELETE FROM network_stats WHERE date = :date")
    suspend fun deleteStatsForDate(date: String)
    
    /**
     * حذف الإحصائيات الأقدم من تاريخ محدد
     */
    @Query("DELETE FROM network_stats WHERE date < :date")
    suspend fun deleteStatsOlderThan(date: String)
    
    /**
     * حذف جميع الإحصائيات
     */
    @Query("DELETE FROM network_stats")
    suspend fun deleteAllStats()
    
    /**
     * زيادة عداد Cache Hit
     */
    @Query("""
        UPDATE network_stats 
        SET cacheHitCount = cacheHitCount + 1 
        WHERE date = date('now')
    """)
    suspend fun incrementCacheHit()
    
    /**
     * زيادة عداد Cache Miss
     */
    @Query("""
        UPDATE network_stats 
        SET cacheMissCount = cacheMissCount + 1 
        WHERE date = date('now')
    """)
    suspend fun incrementCacheMiss()
    
    /**
     * إضافة بيانات موفرة
     */
    @Query("""
        UPDATE network_stats 
        SET dataSavedFromCache = dataSavedFromCache + :bytes 
        WHERE date = date('now')
    """)
    suspend fun addDataSaved(bytes: Long)
    
    /**
     * إضافة بيانات مستهلكة
     */
    @Query("""
        UPDATE network_stats 
        SET totalDataUsed = totalDataUsed + :bytes 
        WHERE date = date('now')
    """)
    suspend fun addDataUsed(bytes: Long)
    
    /**
     * زيادة وقت تشغيل VPN
     */
    @Query("""
        UPDATE network_stats 
        SET vpnActiveMinutes = vpnActiveMinutes + :minutes 
        WHERE date = date('now')
    """)
    suspend fun addVpnActiveTime(minutes: Int)
    
    /**
     * زيادة عداد تبديل الشبكة
     */
    @Query("""
        UPDATE network_stats 
        SET networkSwitchCount = networkSwitchCount + 1 
        WHERE date = date('now')
    """)
    suspend fun incrementNetworkSwitch()
    
    /**
     * إضافة موارد Prefetch
     */
    @Query("""
        UPDATE network_stats 
        SET prefetchedResourcesCount = prefetchedResourcesCount + :count,
            prefetchedDataSize = prefetchedDataSize + :bytes
        WHERE date = date('now')
    """)
    suspend fun addPrefetchedData(count: Int, bytes: Long)
    
    /**
     * الحصول على إجمالي البيانات الموفرة
     */
    @Query("SELECT COALESCE(SUM(dataSavedFromCache), 0) FROM network_stats")
    suspend fun getTotalDataSaved(): Long
    
    /**
     * الحصول على إجمالي البيانات المستهلكة
     */
    @Query("SELECT COALESCE(SUM(totalDataUsed), 0) FROM network_stats")
    suspend fun getTotalDataUsed(): Long
    
    /**
     * الحصول على متوسط نسبة Cache Hit
     */
    @Query("""
        SELECT AVG(
            CASE 
                WHEN (cacheHitCount + cacheMissCount) > 0 
                THEN (cacheHitCount * 100.0) / (cacheHitCount + cacheMissCount)
                ELSE 0 
            END
        ) FROM network_stats
    """)
    suspend fun getAverageCacheHitRatio(): Double
    
    /**
     * الحصول على إجمالي وقت تشغيل VPN
     */
    @Query("SELECT COALESCE(SUM(vpnActiveMinutes), 0) FROM network_stats")
    suspend fun getTotalVpnActiveTime(): Int
    
    /**
     * مراقبة إحصائيات اليوم
     */
    @Query("SELECT * FROM network_stats WHERE date = date('now') LIMIT 1")
    fun observeTodayStats(): Flow<NetworkStats?>
    
    /**
     * مراقبة آخر N أيام
     */
    @Query("SELECT * FROM network_stats ORDER BY date DESC LIMIT :days")
    fun observeLastDaysStats(days: Int): Flow<List<NetworkStats>>
    
    /**
     * الحصول على إحصائيات شاملة
     */
    @Query("""
        SELECT 
            COUNT(*) as totalDays,
            COALESCE(SUM(totalDataUsed), 0) as totalDataUsed,
            COALESCE(SUM(dataSavedFromCache), 0) as totalDataSaved,
            COALESCE(SUM(cacheHitCount), 0) as totalCacheHits,
            COALESCE(SUM(cacheMissCount), 0) as totalCacheMisses,
            COALESCE(SUM(vpnActiveMinutes), 0) as totalVpnMinutes,
            COALESCE(SUM(networkSwitchCount), 0) as totalNetworkSwitches,
            COALESCE(SUM(prefetchedResourcesCount), 0) as totalPrefetchedResources,
            COALESCE(SUM(prefetchedDataSize), 0) as totalPrefetchedData
        FROM network_stats
    """)
    suspend fun getOverallStatistics(): OverallStatistics
}

/**
 * نموذج بيانات الإحصائيات الشاملة
 */
data class OverallStatistics(
    val totalDays: Int,
    val totalDataUsed: Long,
    val totalDataSaved: Long,
    val totalCacheHits: Long,
    val totalCacheMisses: Long,
    val totalVpnMinutes: Int,
    val totalNetworkSwitches: Int,
    val totalPrefetchedResources: Int,
    val totalPrefetchedData: Long
) {
    val overallCacheHitRatio: Float
        get() = if (totalCacheHits + totalCacheMisses > 0) {
            totalCacheHits.toFloat() / (totalCacheHits + totalCacheMisses) * 100
        } else 0f
    
    val overallDataSavingRatio: Float
        get() = if (totalDataUsed + totalDataSaved > 0) {
            totalDataSaved.toFloat() / (totalDataUsed + totalDataSaved) * 100
        } else 0f
}
